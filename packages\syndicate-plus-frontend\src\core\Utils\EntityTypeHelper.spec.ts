import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getEntityTypeForEnvironment } from './EntityTypeHelper';
import { EntityType } from '@core/enums/EntityType';
import LocalStorageService from '@services/LocalStorageService';
import * as EnvironmentHelper from '@helpers/EnvironmentHelper';

// Mock dependencies
vi.mock('@services/LocalStorageService', () => ({
  default: {
    getCustomEntityType: vi.fn(),
    getEnvironmentGlobalId: vi.fn(),
  },
}));

vi.mock('@helpers/EnvironmentHelper', () => ({
  getEnvironmentGlobalId: vi.fn(),
}));

describe('EntityTypeHelper', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getEntityTypeForEnvironment', () => {
    it('returns custom entity type when available', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockReturnValueOnce(EntityType.SKU);

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe(EntityType.SKU);
      expect(LocalStorageService.getCustomEntityType).toHaveBeenCalled();
      expect(LocalStorageService.getEnvironmentGlobalId).not.toHaveBeenCalled();
      expect(EnvironmentHelper.getEnvironmentGlobalId).not.toHaveBeenCalled();
    });

    it('falls back to localStorage environment when custom type is not set', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockReturnValueOnce(null);
      vi.mocked(LocalStorageService.getEnvironmentGlobalId).mockReturnValueOnce('prod-marshallgroup-prod');

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe(EntityType.ITEM);
      expect(LocalStorageService.getCustomEntityType).toHaveBeenCalled();
      expect(LocalStorageService.getEnvironmentGlobalId).toHaveBeenCalled();
      expect(EnvironmentHelper.getEnvironmentGlobalId).not.toHaveBeenCalled();
    });

    it('uses environment helper when localStorage environment is not set', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockReturnValueOnce(null);
      vi.mocked(LocalStorageService.getEnvironmentGlobalId).mockReturnValueOnce(null);
      vi.mocked(EnvironmentHelper.getEnvironmentGlobalId).mockReturnValueOnce('prod-marshallgroup-prod');

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe(EntityType.ITEM);
      expect(LocalStorageService.getCustomEntityType).toHaveBeenCalled();
      expect(LocalStorageService.getEnvironmentGlobalId).toHaveBeenCalled();
      expect(EnvironmentHelper.getEnvironmentGlobalId).toHaveBeenCalled();
    });

    it('returns PRODUCT for non-marshallgroup environments', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockReturnValueOnce(null);
      vi.mocked(LocalStorageService.getEnvironmentGlobalId).mockReturnValueOnce('some-other-env');

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe(EntityType.PRODUCT);
    });

    it('returns PRODUCT when error occurs', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockImplementationOnce(() => {
        throw new Error('Test error');
      });

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe(EntityType.PRODUCT);
    });

    it('accepts any custom entity type value', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockReturnValueOnce('CUSTOM_TYPE');

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe('CUSTOM_TYPE');
      expect(LocalStorageService.getCustomEntityType).toHaveBeenCalled();
      expect(LocalStorageService.getEnvironmentGlobalId).not.toHaveBeenCalled();
    });

    it('preserves the exact custom entity type case', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockReturnValueOnce('lowercasetype');

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe('lowercasetype'); // Should preserve the original case
      expect(LocalStorageService.getCustomEntityType).toHaveBeenCalled();
      expect(LocalStorageService.getEnvironmentGlobalId).not.toHaveBeenCalled();
    });

    it('works with any custom entity type value', () => {
      // Arrange
      vi.mocked(LocalStorageService.getCustomEntityType).mockReturnValueOnce('CUSTOM_STRING_NOT_IN_ENUM');

      // Act
      const result = getEntityTypeForEnvironment();

      // Assert
      expect(result).toBe('CUSTOM_STRING_NOT_IN_ENUM'); // Should preserve the exact value
      expect(LocalStorageService.getCustomEntityType).toHaveBeenCalled();
      expect(LocalStorageService.getEnvironmentGlobalId).not.toHaveBeenCalled();
    });
  });
});
