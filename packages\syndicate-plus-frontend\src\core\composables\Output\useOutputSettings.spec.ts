import { describe, it, expect } from 'vitest';
import { useOutputSettings } from './index';
import { OutputType, DataLevel, DeliveryMethod, ExcelOutputMode } from '@core/enums';
import { OutputExcelSettings } from '@core/interfaces/Outputs';

describe('useOutputSettings', () => {
  describe('setDeliveryMethodsFromList', () => {
    it('should set DeliveryMethods string from DeliveryMethodsList and clean unused default settings values', async () => {
      const { newOutput, setDeliveryMethodsFromList } = useOutputSettings();
      newOutput.value.Settings.DeliveryMethodsList = [DeliveryMethod.AZURE_BLOB, DeliveryMethod.FTP];
      setDeliveryMethodsFromList();
      expect(newOutput.value.Settings.DeliveryMethods).toBe(`${DeliveryMethod.AZURE_BLOB};${DeliveryMethod.FTP}`);
      expect(newOutput.value.Settings.DeliveryMethodsList).toBe(undefined);
      expect(newOutput.value.Settings.FTPS_FileName).toBe(undefined);
      expect(newOutput.value.Settings.Http_Filename).toBe(undefined);
      expect(newOutput.value.Settings.SFTP_FileName).toBe(undefined);
    });
  });

  describe('setMultiTabWorksheetStartCells', () => {
    it('should set values to output model from worksheet pairs', async () => {
      const { newOutput, setMultiTabWorksheetStartCells, worksheetPairs } = useOutputSettings();

      // Modify the first pair that's initialized by default
      worksheetPairs.value[0].worksheetName = 'worksheetName';
      worksheetPairs.value[0].startCell = 'B10';

      setMultiTabWorksheetStartCells();

      const expectedValue = JSON.stringify({
        worksheetName: 'B10',
      });
      expect((newOutput.value.Settings as OutputExcelSettings).MultiTabWorksheetStartCells).toBe(expectedValue);
      expect((newOutput.value.Settings as OutputExcelSettings).ExportExcelWorksheetsName).toBe('worksheetName');
      expect((newOutput.value.Settings as OutputExcelSettings).Mode).toBe(ExcelOutputMode.SINGLE_TAB);
    });

    it('should set values for multiple worksheets including dynamic pairs', async () => {
      const { newOutput, setMultiTabWorksheetStartCells, worksheetPairs } = useOutputSettings();

      // Modify the first pair and add more
      worksheetPairs.value[0].worksheetName = 'MainSheet';
      worksheetPairs.value[0].startCell = 'A1';

      // Add dynamic worksheet pairs
      worksheetPairs.value.push(
        { worksheetName: 'Sheet2', startCell: 'B5' },
        { worksheetName: 'Sheet3', startCell: 'C10' }
      );

      setMultiTabWorksheetStartCells();

      const expectedValue = JSON.stringify({
        MainSheet: 'A1',
        Sheet2: 'B5',
        Sheet3: 'C10',
      });
      expect((newOutput.value.Settings as OutputExcelSettings).MultiTabWorksheetStartCells).toBe(expectedValue);
      expect((newOutput.value.Settings as OutputExcelSettings).ExportExcelWorksheetsName).toBe('MainSheet');
      expect((newOutput.value.Settings as OutputExcelSettings).Mode).toBe(ExcelOutputMode.MULTI_TAB_BASIC);
    });

    it('should ignore empty worksheet pairs', async () => {
      const { newOutput, setMultiTabWorksheetStartCells, worksheetPairs } = useOutputSettings();

      // Modify the first pair
      worksheetPairs.value[0].worksheetName = 'MainSheet';
      worksheetPairs.value[0].startCell = 'A1';

      // Add dynamic worksheet pairs with some empty values
      worksheetPairs.value.push(
        { worksheetName: 'Sheet2', startCell: 'B5' },
        { worksheetName: '', startCell: 'C10' }, // Empty worksheet name
        { worksheetName: 'Sheet3', startCell: '' }, // Empty start cell
        { worksheetName: 'Sheet4', startCell: 'D15' }
      );

      setMultiTabWorksheetStartCells();

      const expectedValue = JSON.stringify({
        MainSheet: 'A1',
        Sheet2: 'B5',
        Sheet4: 'D15',
      });
      expect((newOutput.value.Settings as OutputExcelSettings).MultiTabWorksheetStartCells).toBe(expectedValue);
      expect((newOutput.value.Settings as OutputExcelSettings).ExportExcelWorksheetsName).toBe('MainSheet');
      expect((newOutput.value.Settings as OutputExcelSettings).Mode).toBe(ExcelOutputMode.MULTI_TAB_BASIC);
    });
  });

  describe('addWorksheetPair', () => {
    it('should add a new worksheet pair with default values', async () => {
      const { worksheetPairs, addWorksheetPair } = useOutputSettings();
      expect(worksheetPairs.value.length).toBe(1); // Starts with one pair

      addWorksheetPair();

      expect(worksheetPairs.value.length).toBe(2);
      expect(worksheetPairs.value[1]).toEqual({
        worksheetName: '',
        startCell: 'A10',
      });
    });

    it('should add multiple worksheet pairs', async () => {
      const { worksheetPairs, addWorksheetPair } = useOutputSettings();

      addWorksheetPair();
      addWorksheetPair();

      expect(worksheetPairs.value.length).toBe(3); // Started with 1, added 2
      expect(worksheetPairs.value[0]).toEqual({
        worksheetName: 'SheetName1',
        startCell: 'A10',
      });
      expect(worksheetPairs.value[1]).toEqual({
        worksheetName: '',
        startCell: 'A10',
      });
      expect(worksheetPairs.value[2]).toEqual({
        worksheetName: '',
        startCell: 'A10',
      });
    });
  });

  describe('removeWorksheetPair', () => {
    it('should remove worksheet pair at specified index', async () => {
      const { worksheetPairs, addWorksheetPair, removeWorksheetPair } = useOutputSettings();

      // Add some pairs (starts with 1, add 2 more)
      addWorksheetPair();
      addWorksheetPair();

      // Modify them to be different
      worksheetPairs.value[0].worksheetName = 'Sheet1';
      worksheetPairs.value[1].worksheetName = 'Sheet2';
      worksheetPairs.value[2].worksheetName = 'Sheet3';

      expect(worksheetPairs.value.length).toBe(3);

      // Remove middle one
      removeWorksheetPair(1);

      expect(worksheetPairs.value.length).toBe(2);
      expect(worksheetPairs.value[0].worksheetName).toBe('Sheet1');
      expect(worksheetPairs.value[1].worksheetName).toBe('Sheet3');
    });

    it('should not remove when only one pair exists', async () => {
      const { worksheetPairs, removeWorksheetPair } = useOutputSettings();

      expect(worksheetPairs.value.length).toBe(1); // Starts with one pair

      // This should not remove the last pair
      removeWorksheetPair(0);

      expect(worksheetPairs.value.length).toBe(1); // Should still have one pair
    });
  });

  describe('areAllPairsValid', () => {
    it('should return true when all pairs are valid', async () => {
      const { worksheetPairs, areAllPairsValid } = useOutputSettings();

      worksheetPairs.value[0].worksheetName = 'Sheet1';
      worksheetPairs.value[0].startCell = 'A10';

      expect(areAllPairsValid.value).toBe(true);
    });

    it('should return false when any pair has empty worksheet name', async () => {
      const { worksheetPairs, areAllPairsValid, addWorksheetPair } = useOutputSettings();

      worksheetPairs.value[0].worksheetName = 'Sheet1';
      worksheetPairs.value[0].startCell = 'A10';

      addWorksheetPair();
      worksheetPairs.value[1].worksheetName = ''; // Empty name
      worksheetPairs.value[1].startCell = 'B10';

      expect(areAllPairsValid.value).toBe(false);
    });

    it('should return false when any pair has invalid start cell format', async () => {
      const { worksheetPairs, areAllPairsValid } = useOutputSettings();

      worksheetPairs.value[0].worksheetName = 'Sheet1';
      worksheetPairs.value[0].startCell = 'invalid'; // Invalid format

      expect(areAllPairsValid.value).toBe(false);
    });
  });

  describe('isMultiTabMode', () => {
    it('should return false when only one pair exists', async () => {
      const { isMultiTabMode } = useOutputSettings();

      expect(isMultiTabMode.value).toBe(false);
    });

    it('should return true when multiple pairs exist', async () => {
      const { worksheetPairs, isMultiTabMode, addWorksheetPair } = useOutputSettings();

      addWorksheetPair();

      expect(isMultiTabMode.value).toBe(true);
    });
  });

  describe('firstWorksheetName', () => {
    it('should return the name of the first worksheet', async () => {
      const { worksheetPairs, firstWorksheetName } = useOutputSettings();

      worksheetPairs.value[0].worksheetName = 'MySheet';

      expect(firstWorksheetName.value).toBe('MySheet');
    });

    it('should return empty string when no pairs exist', async () => {
      const { worksheetPairs, firstWorksheetName } = useOutputSettings();

      worksheetPairs.value = [];

      expect(firstWorksheetName.value).toBe('');
    });
  });

  describe('enableCompressionIsChanged', () => {
    it('should set outputFormat to outputType if enableCompression is false', async () => {
      const { enableCompressionIsChanged, newOutput } = useOutputSettings();
      newOutput.value.Settings.EnableCompression = false;
      enableCompressionIsChanged();
      expect(newOutput.value.Settings.OutputFormat).toBe(OutputType.XML);
    });

    it('should set outputFormat to ZIP if enableCompression is true', async () => {
      const { enableCompressionIsChanged, newOutput } = useOutputSettings();
      newOutput.value.Settings.OutputFormat = OutputType.XML;
      (newOutput.value.Settings as OutputExcelSettings).UseZip = false;
      newOutput.value.Settings.EnableCompression = true;
      enableCompressionIsChanged();
      expect(newOutput.value.Settings.OutputFormat).toBe(OutputType.ZIP);
      expect((newOutput.value.Settings as OutputExcelSettings).UseZip).toBe(true);
    });
  });

  describe('onOutputTypeChange', () => {
    it('should set outputFormat to outputType if enableCompression is false', async () => {
      const { onOutputTypeChange, newOutput } = useOutputSettings();
      newOutput.value.Settings.EnableCompression = false;
      newOutput.value.OutputType = OutputType.XML;
      onOutputTypeChange();
      expect(newOutput.value.Settings.OutputFormat).toBe(OutputType.XML);
    });

    it('should not set outputFormat to outputType if enableCompression is true', async () => {
      const { onOutputTypeChange, newOutput, enableCompressionIsChanged } = useOutputSettings();
      newOutput.value.Settings.EnableCompression = true;
      enableCompressionIsChanged();
      newOutput.value.OutputType = OutputType.XML;
      onOutputTypeChange();
      expect(newOutput.value.Settings.OutputFormat).toBe(OutputType.ZIP);
    });

    it('should set exportToZipFolder to true if outputType is excel and enableCompression is true', async () => {
      const { onOutputTypeChange, newOutput, enableCompressionIsChanged } = useOutputSettings();
      newOutput.value.Settings.EnableCompression = true;
      enableCompressionIsChanged();
      newOutput.value.OutputType = OutputType.EXCEL;
      onOutputTypeChange();
      expect((newOutput.value.Settings as OutputExcelSettings).UseZip).toBe(true);
    });

    it('should remove exportToZipFolder from model if outputType is not excel and enableCompression is true', async () => {
      const { onOutputTypeChange, newOutput, enableCompressionIsChanged } = useOutputSettings();
      newOutput.value.Settings.EnableCompression = true;
      enableCompressionIsChanged();
      newOutput.value.OutputType = OutputType.CSV;
      onOutputTypeChange();
      expect((newOutput.value.Settings as OutputExcelSettings).UseZip).toBe(undefined);
    });

    it('should set default datalevel and delimiter if output type is Csv', () => {
      const { onOutputTypeChange, newOutput } = useOutputSettings();
      const delimiter = ';';
      newOutput.value.OutputType = OutputType.CSV;
      onOutputTypeChange();
      expect((newOutput.value.Settings as any).Delimiter).toBe(delimiter);
      expect((newOutput.value.Settings as any).DataLevel).toBe(DataLevel.BASIC_DATA);
    });

    it('should remove delimiter and datalevel if output type is not Csv', () => {
      const { onOutputTypeChange, newOutput } = useOutputSettings();
      newOutput.value.OutputType = OutputType.CSV;
      onOutputTypeChange();
      newOutput.value.OutputType = OutputType.XML;
      onOutputTypeChange();
      expect((newOutput.value.Settings as any).Delimiter).toBe(undefined);
      expect((newOutput.value.Settings as any).DataLevel).toBe(undefined);
      expect('DataLevel' in (newOutput.value.Settings as any)).toBe(false);
      expect('Delimiter' in (newOutput.value.Settings as any)).toBe(false);
    });

    it('should set default values if output type is Excel', () => {
      const { onOutputTypeChange, newOutput } = useOutputSettings();
      newOutput.value.OutputType = OutputType.EXCEL;
      onOutputTypeChange();
      expect((newOutput.value.Settings as OutputExcelSettings).ExportExcelWorksheetsName).toBe('SheetName1');
      expect((newOutput.value.Settings as OutputExcelSettings).Mode).toBe(ExcelOutputMode.SINGLE_TAB);
      expect((newOutput.value.Settings as OutputExcelSettings).MultiTabWorksheetStartCells).toBe('');
      expect((newOutput.value.Settings as OutputExcelSettings).TemplateFilename).toBe('MyTemplateFileName.xlsx');
      expect((newOutput.value.Settings as OutputExcelSettings).ExportFilename).toBe('MyOutputFileName.xlsx');
      expect((newOutput.value.Settings as OutputExcelSettings).UseZip).toBe(false);
      expect((newOutput.value.Settings as OutputExcelSettings).UseUTF8).toBe(false);
    });

    it('should remove default values if output type is not Excel', () => {
      const { onOutputTypeChange, newOutput } = useOutputSettings();
      newOutput.value.OutputType = OutputType.EXCEL;
      onOutputTypeChange();
      newOutput.value.OutputType = OutputType.CSV;
      onOutputTypeChange();
      expect('ExportExcelWorksheetsName' in (newOutput.value.Settings as OutputExcelSettings)).toBe(false);
      expect('Mode' in (newOutput.value.Settings as OutputExcelSettings)).toBe(false);
      expect('MultiTabWorksheetStartCells' in (newOutput.value.Settings as OutputExcelSettings)).toBe(false);
      expect('TemplateFilename' in (newOutput.value.Settings as OutputExcelSettings)).toBe(false);
      expect('ExportFilename' in (newOutput.value.Settings as OutputExcelSettings)).toBe(false);
      expect('UseZip' in (newOutput.value.Settings as OutputExcelSettings)).toBe(false);
      expect('UseUTF8' in (newOutput.value.Settings as OutputExcelSettings)).toBe(false);
    });
  });

  describe('isCSVOutput', () => {
    it('should return true if outputType is CSV', async () => {
      const { isCSVOutput, newOutput } = useOutputSettings();
      newOutput.value.OutputType = OutputType.CSV;
      expect(isCSVOutput.value).toBe(true);
    });

    it('should return false if outputType is not CSV', async () => {
      const { isCSVOutput, newOutput } = useOutputSettings();
      newOutput.value.OutputType = OutputType.XML;
      expect(isCSVOutput.value).toBe(false);
    });
  });

  describe('isAzureDelivery', () => {
    it('should return true if DeliveryMethods include AZURE_BLOB', async () => {
      const { isAzureDelivery, newOutput } = useOutputSettings();
      newOutput.value.Settings.DeliveryMethodsList?.push(DeliveryMethod.AZURE_BLOB);
      expect(isAzureDelivery.value).toBe(true);
    });

    it('should return false if DeliveryMethods do not include AZURE_BLOB', async () => {
      const { isAzureDelivery, newOutput } = useOutputSettings();
      newOutput.value.Settings.DeliveryMethodsList = [];
      expect(isAzureDelivery.value).toBe(false);
    });
  });
});
