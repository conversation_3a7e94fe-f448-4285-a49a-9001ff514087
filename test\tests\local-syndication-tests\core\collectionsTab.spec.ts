import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { CorePage } from '@pages/core/core.page';
import { CoreCollectionsTabPage } from '@pages/core/coreCollectionsTab.page';
import { CoreHistoryTabPage } from '@pages/core/coreHistoryTab.page';
import { SettingPage } from '@pages/notCore/settings/settings.page';

test.describe('Collections Tab', () => {
  const UITestFormat = 'UITest format';
  const workAreaProductStatic = 'UiTest-Products-Static';
  const channel = 'UI Test - Channel 01';
  const node01Channel01 = 'UI Test - Node 01 (UI Test - Channel 01)';
  const node04Channel01 = 'UI Test - empty  Node 04 (UI Test - Channel 01)';
  const UITestMapping = 'UITestMapping';
  const UITestMappingXML = 'UITestMapping-XML';
  const UITestProductMapping = 'UITest - ProductFormatMapping';
  const EXCELOutput = 'UITestEXCELproducts (excel)';
  const CSVOutput = 'UITestCSV (csv)';
  const XMLOutput = 'UITestXML (xml)';
  const finishedStatus = 'Finished';
  const reviewPageTitle01 = 'review - UITestMapping - UiTest-Products-Static';
  const reviewPageTitleNode01 = 'review - UITest - ProductFormatMapping - UI Test - Node 01 (UI Test - Channel 01)';
  const reviewPageTitleEmptyNode04 =
    'review - UITest - ProductFormatMapping - UI Test - empty  Node 04 (UI Test - Channel 01)';
  const entityId01 = '22683';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let corePage: CorePage;
  let coreCollectionsTabPage: CoreCollectionsTabPage;
  let coreHistoryTabPage: CoreHistoryTabPage;
  let settingPage: SettingPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    corePage = new CorePage(localPage);
    coreCollectionsTabPage = new CoreCollectionsTabPage(localPage);
    coreHistoryTabPage = new CoreHistoryTabPage(localPage);
    settingPage = new SettingPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.openFormat(UITestFormat);
    await corePage.historyTab.click();
    await localPage.waitForTimeout(2000); // Wait for the table to load
    //remove all collections from history tab
    await expect(async () => {
      const collectionsToDelete = [workAreaProductStatic, channel, node01Channel01];
      for (const collectionName of collectionsToDelete) {
        let matchingCollections = coreHistoryTabPage.getByCollectionName(collectionName);
        while ((await matchingCollections.count()) > 0) {
          // Delete the first visible collection in the list
          const collection = matchingCollections.first();
          if (await collection.isVisible()) {
            await collection.click();
            await coreHistoryTabPage.deleteButton.click();
            await coreHistoryTabPage.confirmDeleteButton.click();
            await localPage.waitForTimeout(2000); // Wait for the deletion to complete
          }
          // Refresh the list of matching collections after deletion
          matchingCollections = coreHistoryTabPage.getByCollectionName(collectionName);
        }
      }
    }).toPass();
    await corePage.collectionsTab.click();
  });

  test('Syndicate workarea', async () => {
    test.slow(true, 'Syndication with excel output is slow');
    await coreCollectionsTabPage.selectCollection(workAreaProductStatic).click();
    await coreCollectionsTabPage.syndicateClick();
    await expect(coreCollectionsTabPage.syndicateTitle, 'Dialog title is not visible').toBeVisible();
    await coreCollectionsTabPage.selectMappingAndOutput(UITestMapping, EXCELOutput);
    await coreCollectionsTabPage.saveButton.click();
    await corePage.historyTab.click();
    // To wait that the job has finish running:
    await expect(async () => {
      await corePage.clickProductsTab(); // To refresh the history tab column
      await corePage.clickHistoryTab();
      await expect(coreHistoryTabPage.getByCollectionAndStatus(workAreaProductStatic, finishedStatus)).toBeVisible({
        timeout: 3000,
      });
    }, 'Could not find template').toPass();
  });

  test('Syndicate channel', async () => {
    await coreCollectionsTabPage.selectCollection(node01Channel01).click();
    await coreCollectionsTabPage.syndicateClick();
    await coreCollectionsTabPage.selectMappingAndOutput(UITestProductMapping, CSVOutput);
    await coreCollectionsTabPage.saveButton.click();
    await expect(async () => {
      await corePage.collectionsTab.click();
      await corePage.historyTab.click();
      await expect(coreHistoryTabPage.getByCollectionAndStatus(channel, finishedStatus)).toBeVisible({ timeout: 2000 });
    }).toPass();
  });

  test('Review workarea', async () => {
    await coreCollectionsTabPage.selectCollection(workAreaProductStatic).click();
    await coreCollectionsTabPage.reviewClick();
    await expect.soft(coreCollectionsTabPage.reviewTitle, 'Dialog title is not visible').toBeVisible();
    await coreCollectionsTabPage.selectMapping(UITestMapping);
    await coreCollectionsTabPage.reviewDialogButton.click();
    await expect.soft(coreCollectionsTabPage.reviewPageTitle(reviewPageTitle01)).toBeVisible();
    await expect.soft(coreCollectionsTabPage.reviewFilter, 'Filter button is not visible').toBeVisible();
    await expect(coreCollectionsTabPage.getByRollName(entityId01), 'Review table was not load correctly').toBeVisible();
  });

  test('Review channel', async () => {
    await coreCollectionsTabPage.selectCollection(node01Channel01).click();
    await coreCollectionsTabPage.reviewClick();
    await coreCollectionsTabPage.selectMapping(UITestProductMapping);
    await coreCollectionsTabPage.reviewDialogButton.click();
    await expect.soft(coreCollectionsTabPage.reviewPageTitle(reviewPageTitleNode01)).toBeVisible();
    await expect.soft(coreCollectionsTabPage.reviewFilter, 'Filter button is not visible').toBeVisible();
    await expect(coreCollectionsTabPage.getByRollName(entityId01), 'Review table was not load correctly').toBeVisible();
  });

  // This test is a known issue. We'll fix it in the future.
  test.fail('Review empty channel @notForPr', async () => {
    await coreCollectionsTabPage.selectCollection(node04Channel01).click();
    await coreCollectionsTabPage.reviewClick();
    await coreCollectionsTabPage.selectMapping(UITestProductMapping);
    await coreCollectionsTabPage.reviewDialogButton.click();
    await expect.soft(coreCollectionsTabPage.reviewPageTitle(reviewPageTitleEmptyNode04)).toBeVisible();
    await expect.soft(coreCollectionsTabPage.reviewFilter, 'Filter button is not visible').toBeVisible();
    await expect(coreCollectionsTabPage.spinner, 'Spinner is visible').toBeHidden();
  });

  test('All mappings and outputs are visible', async () => {
    // await coreCollectionsTabPage.selectCollection(workAreaProductStatic).click();
    await coreCollectionsTabPage.selectCollection(node01Channel01).click();
    await coreCollectionsTabPage.syndicateClick();
    await coreCollectionsTabPage.selectMapping(UITestMappingXML);
    await coreCollectionsTabPage.outputPlaceholder.click();
    await expect(coreCollectionsTabPage.selectFromList(XMLOutput)).toBeVisible();
    await coreCollectionsTabPage.selectMapping(UITestMapping);
    await coreCollectionsTabPage.outputPlaceholder.click();
    await expect(coreCollectionsTabPage.selectFromList(EXCELOutput)).toBeVisible();
    await coreCollectionsTabPage.outputPlaceholder.click();
    await coreCollectionsTabPage.cancelButton.click();
    await expect(coreCollectionsTabPage.selectCollection(node01Channel01)).toBeVisible();
  });

  test('Collections are visible', async () => {
    await expect(coreCollectionsTabPage.selectCollection(workAreaProductStatic)).toBeVisible();
    await expect(coreCollectionsTabPage.selectCollection(node01Channel01)).toBeVisible();
    // Navigate back and forth to check if the collections are still visible
    await coreCollectionsTabPage.goBackButton.click();
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await settingPage.goBackButton.click();
    await welcomeToSyndicatePlusPage.openFormat(UITestFormat);
    await expect(coreCollectionsTabPage.selectCollection(workAreaProductStatic)).toBeVisible();
    await expect(coreCollectionsTabPage.selectCollection(node01Channel01)).toBeVisible();
  });
});
