import { Locator, Page } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';

export class EditMappingPage extends BasePage {
  private page: Page;
  readonly saveButton: Locator;
  readonly cancelButton: Locator;
  readonly mapAutoButton: Locator;
  readonly unmapButton: Locator;
  readonly addListItemButton: Locator;
  readonly removeListItemButton: Locator;
  readonly mapEnumerationButton: Locator;
  readonly goBackButton: Locator;
  readonly fieldsTab: Locator;
  readonly functionsTab: Locator;
  // fields locators
  readonly entityTypeDropdown: Locator;
  readonly resourcesDropdown: Locator;
  readonly entityField = (name: string): Locator =>
    this.page.locator('#main-section').locator('div').locator('.element-id').getByText(name);
  readonly fieldsDropdown: Locator;
  readonly source = (fieldName: string): Locator => this.page.locator('td').filter({ hasText: fieldName });
  readonly target = (target: string): Locator => this.page.getByRole('row', { name: target }).locator('div').nth(1);
  readonly resourceExportDropdown: Locator;
  readonly saveChangesToaster: Locator;
  // filters locators
  readonly categoryFilter: Locator;
  readonly fieldTypeFilter: Locator;
  readonly stateFilter: Locator;
  readonly clearStateFilterButton: Locator;
  readonly fieldSearchBar: Locator;
  // dialog locators
  readonly dialogHeader: Locator;
  readonly dialogOkButton: Locator;
  readonly dialogCancelButton: Locator;
  readonly dialogCloseButton: Locator;
  // map enumeration dialog locators
  readonly enumDialogHeader: Locator;
  readonly enumDialogTarget = (number: number): Locator =>
    this.page.locator('[placeholder="target value"]').nth(number);
  readonly enumDialogTargetWithText = (text: string, number: number): Locator =>
    this.page.locator('[placeholder="target value"]').getByText(text).nth(number);
  readonly selectFromList = (text: string): Locator => this.page.getByRole('option', { name: text, exact: true });
  readonly enumDialogSaveButton: Locator;
  readonly enumDialogAutomapButton: Locator;
  readonly enumDialogCancelButton: Locator;
  // functions locators
  readonly newFunctionButton: Locator;
  readonly functionNameField: Locator;
  readonly validateFunctionButton: Locator;
  readonly saveFunctionButton: Locator;
  readonly functionSavedToaster: Locator;
  readonly functionFailedToSaveToaster: Locator;
  readonly functionDeletedToaster: Locator;
  readonly functionByName = (name: string): Locator =>
    this.page.locator('.function-card').getByText(name, { exact: true });
  readonly functionEditButton = (name: string): Locator => this.functionByName(name).locator('.edit-custom-function');
  readonly functionDeleteButton = (name: string): Locator =>
    this.functionByName(name).locator('.delete-custom-function');

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.saveButton = page.getByLabel('save');
    this.cancelButton = page.getByLabel('cancel');
    this.mapAutoButton = page.getByLabel('map automatically');
    this.unmapButton = page.getByLabel('unmap source field');
    this.addListItemButton = page.getByLabel('add list item');
    this.removeListItemButton = page.getByLabel('remove list item');
    this.mapEnumerationButton = page.getByLabel('map enumeration');
    this.goBackButton = page.getByLabel('go back');
    this.fieldsTab = page.getByRole('tab', { name: 'fields' });
    this.functionsTab = page.getByRole('tab', { name: 'functions' });
    this.entityTypeDropdown = page.locator('.q-item__section').getByText('Product');
    this.resourcesDropdown = page.locator('.q-item__section').getByText('Resources');
    this.fieldsDropdown = page.locator('.q-item__section').getByText('fields');
    this.resourceExportDropdown = page.locator('.q-item__section').getByText('resource export');
    this.saveChangesToaster = page.getByText('changes saved successfully');
    this.categoryFilter = page.locator('label').filter({ hasText: 'category' }).locator('i');
    this.fieldTypeFilter = page.locator('label').filter({ hasText: 'field type' }).locator('i');
    this.stateFilter = page.locator('label').filter({ hasText: 'state' }).locator('i');
    this.clearStateFilterButton = page.locator('label').filter({ hasText: 'state' }).locator('button');
    this.dialogHeader = page.getByRole('heading');
    this.dialogOkButton = page.getByRole('button', { name: 'ok' });
    this.dialogCancelButton = page.locator('button').filter({ hasText: 'cancel' });
    this.dialogCloseButton = page.getByLabel('close');
    this.enumDialogHeader = page.getByRole('heading', { name: 'map enumeration' });
    this.enumDialogSaveButton = page.getByRole('button', { name: 'save' }).nth(1);
    this.enumDialogAutomapButton = page.getByRole('button', { name: 'automap' });
    this.enumDialogCancelButton = page.getByRole('button', { name: 'cancel' }).nth(1);
    this.newFunctionButton = page.locator('.new');
    this.functionNameField = page.getByRole('textbox', { name: 'function name' });
    this.validateFunctionButton = page.getByRole('button', { name: 'validate function' });
    this.saveFunctionButton = page.locator('button').filter({ hasText: 'save' });
    this.functionSavedToaster = page.getByText('custom function saved', { exact: true });
    this.functionFailedToSaveToaster = page.getByText('a function named UITest function already exists', {
      exact: true,
    });
    this.functionDeletedToaster = page.getByText('custom function was removed', { exact: true });
  }

  async selectCategory(option: string): Promise<void> {
    this.categoryFilter.click();
    await this.page.getByText(option, { exact: true }).click();
  }

  async selectFieldType(option: string): Promise<void> {
    this.fieldTypeFilter.click();
    await this.page.getByText(option, { exact: true }).click();
  }

  async selectState(option: string): Promise<void> {
    this.stateFilter.click();
    await this.page.getByText(option, { exact: true }).click();
  }

  async fillFunctionName(name: string): Promise<void> {
    await this.functionNameField.fill(name);
  }
}
