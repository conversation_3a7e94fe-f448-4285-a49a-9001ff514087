import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { ThirdPartyMessagesPage } from '@pages/notCore/thirdPartyMessages/thirdPartyMessages.page';
import {getPlatformModifierKey} from "@src/helpers/keyboard-modifiers";

test.describe('Third party messages', () => {
  const year25 = '2025';
  const year24 = '2024';
  const day1 = '1';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let thirdPartyMessagesPage: ThirdPartyMessagesPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    thirdPartyMessagesPage = new ThirdPartyMessagesPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);

    await welcomeToSyndicatePlusPage.thirdPartyMessagesButton.click();
  });

  test('Page opens and loads @notForPr', async () => {
    await expect.soft(thirdPartyMessagesPage.title, 'Title is not visible').toBeVisible();
    await expect.soft(thirdPartyMessagesPage.table, 'Data table is not loaded').toBeVisible();
    await thirdPartyMessagesPage.row.nth(1).click();
    await expect.soft(thirdPartyMessagesPage.detailsButton, 'Details button is not visible').toBeVisible();
    await expect(thirdPartyMessagesPage.downloadButton, 'Download button is not visible').toBeVisible();
  });

  test('Details dialog opens and closes @notForPr', async () => {
    await thirdPartyMessagesPage.row.nth(1).click();
    await thirdPartyMessagesPage.detailsButton.click();
    await expect.soft(thirdPartyMessagesPage.detailsDialog, 'Details dialog is not visible').toBeVisible();
    await expect.soft(thirdPartyMessagesPage.dialogCloseButton, 'Details Close button is not visible').toBeVisible();
    await thirdPartyMessagesPage.dialogCloseButton.click();
    await thirdPartyMessagesPage.dialogCloseButton.waitFor({ state: 'hidden' });
    await expect(thirdPartyMessagesPage.detailsDialog, 'Details dialog is still visible').not.toBeVisible();
  });

  test('Download file @notForPr', async () => {
    await thirdPartyMessagesPage.downloadFile(1);
    await expect(thirdPartyMessagesPage.downloadNotification, 'Download notification is not visible').toBeVisible();
  });

  test('Filter and clear filter @notForPr', async () => {
    await expect(thirdPartyMessagesPage.dateFilterButton, 'Date filter is not visible').toBeVisible();
    await thirdPartyMessagesPage.dateFilterButton.click();
    await expect.soft(thirdPartyMessagesPage.startDateField, 'Start date input is not visible').toBeVisible();
    await expect.soft(thirdPartyMessagesPage.endDateField, 'End date input is not visible').toBeVisible();
    await thirdPartyMessagesPage.pickStartDate(year25, year24, day1);
    await expect.soft(thirdPartyMessagesPage.filterButton, 'Filter button is not visible').toBeVisible();
    await expect.soft(thirdPartyMessagesPage.clearFilterButton, 'Clear filter button is not visible').toBeVisible();
    await thirdPartyMessagesPage.filterButton.click();
    await expect(thirdPartyMessagesPage.row.nth(1), 'Date filter didnt work').not.toBeVisible();
    await thirdPartyMessagesPage.dateFilterButton.click();
    await thirdPartyMessagesPage.clearFilterButton.click();
    await expect(thirdPartyMessagesPage.row.nth(1), 'Filter was not cleared').toBeVisible();
  });

  test('Select multiple rows with shift key @notForPr', async ({ localPage }) => {
    await thirdPartyMessagesPage.row.nth(1).click();
    await localPage.keyboard.down('Shift');
    await thirdPartyMessagesPage.row.nth(5).click();
    await localPage.keyboard.up('Shift');
    await expect(thirdPartyMessagesPage.selectedRows, '5 Rows should be selected').toHaveCount(5);
  });

  test('Select multiple rows with CTRL key @notForPr', async ({ localPage }) => {
    const modifierKey = getPlatformModifierKey();
    await localPage.keyboard.down(modifierKey);
    await thirdPartyMessagesPage.row.nth(1).click();
    await thirdPartyMessagesPage.row.nth(2).click();
    await thirdPartyMessagesPage.row.nth(3).click();
    await thirdPartyMessagesPage.row.nth(4).click();
    await thirdPartyMessagesPage.row.nth(5).click();
    await expect(thirdPartyMessagesPage.selectedRows, '5 Rows should be selected').toHaveCount(5);
  });
});
