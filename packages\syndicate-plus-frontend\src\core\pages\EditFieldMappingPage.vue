<template>
  <c-layout-with-back-button :title="title">
    <template #left-sidebar>
      <div id="left-sidebar"></div>
    </template>
    <template #right-sidebar>
      <div id="right-sidebar"></div>
    </template>
    <section id="main-section">
      <q-inner-loading :showing="showLoadingSpinner" color="primary" class="spinner">
        <c-spinner />
      </q-inner-loading>
      <div class="flex flex-nowrap">
        <div class="edit-mapping-tabs">
          <q-tabs
            v-model="leftTab"
            class="bg-white text-grey-7"
            active-color="primary"
            active-class="active-tab"
            indicator-color="transparent"
            align="justify"
            no-caps
          >
            <q-tab :name="EditFieldMappingTabName.FIELDS" :label="$t('core.settings.field_mapping.fields')" />
            <q-tab :name="EditFieldMappingTabName.FUNCTIONS" :label="$t('core.settings.field_mapping.functions')" />
          </q-tabs>
          <div class="fields-functions-panel w-1/5 p-5 overflow-auto">
            <div v-if="isDsaMappingEnabled && rightTab === EditFieldMappingTabName.DSA_MAPPING">
              <!-- Show DSA Fields panel when DSA_MAPPING tab is active -->
              <c-dsa-fields-panel v-show="leftTab === EditFieldMappingTabName.FIELDS" class="fields-panel" />
              <c-functions-panel
                v-show="leftTab === EditFieldMappingTabName.FUNCTIONS"
                class="functions-panel"
                :is-dsa-mapping-panel-active="true"
              />
            </div>
            <div v-else>
              <!-- Show regular Fields panel for all other cases -->
              <c-fields-panel v-show="leftTab === EditFieldMappingTabName.FIELDS" class="fields-panel" />
              <c-functions-panel v-show="leftTab === EditFieldMappingTabName.FUNCTIONS" class="functions-panel" />
            </div>
          </div>
        </div>
        <div class="right-panel w-4/5">
          <!-- Show tabs only when feature flag is enabled -->
          <template v-if="isDsaMappingEnabled">
            <q-tabs
              v-model="rightTab"
              class="bg-white text-grey-7 right-tabs"
              active-color="primary"
              active-class="active-tab"
              indicator-color="transparent"
              align="left"
              no-caps
            >
              <q-tab
                :name="EditFieldMappingTabName.MAPPING"
                :label="$t('core.settings.field_mapping.source_to_target')"
              />
              <q-tab
                :name="EditFieldMappingTabName.DSA_MAPPING"
                :label="$t('core.settings.field_mapping.target_to_dsa')"
              />
            </q-tabs>
            <div class="p-5">
              <c-mapping-panel
                v-show="rightTab === EditFieldMappingTabName.MAPPING"
                :mapping-id="mappingId"
                :save-handler="unifiedSave"
                :is-visible="rightTab === EditFieldMappingTabName.MAPPING"
                class="mapping-panel tab-panel-content"
                @cancel-edit="cancelEdit"
              />
              <c-dsa-mapping-panel
                v-show="rightTab === EditFieldMappingTabName.DSA_MAPPING"
                :save-handler="unifiedSave"
                :is-visible="rightTab === EditFieldMappingTabName.DSA_MAPPING"
                class="dsa-mapping-panel"
                @cancel-edit="cancelEdit"
              />
            </div>
          </template>

          <!-- Show only the mapping panel when feature flag is disabled -->
          <template v-else>
            <div class="p-5">
              <c-mapping-panel
                :mapping-id="mappingId"
                :save-handler="unifiedSave"
                :is-visible="true"
                class="mapping-panel"
                @cancel-edit="cancelEdit"
              />
            </div>
          </template>
        </div>
      </div>
    </section>
  </c-layout-with-back-button>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, computed, onUnmounted } from 'vue';
import { useRouter } from '@composables/useRouter';
import { useAppInsightsStore } from '@stores';
import { storeToRefs } from 'pinia';
import { CFieldsPanel, CDsaFieldsPanel, CMappingPanel, CDsaMappingPanel } from '@core/components/FieldMapping';
import { CFunctionsPanel } from '@core/components/CFunctions';
import { useTradingPartnersStore } from '@stores';
import { PageName } from '@enums';
import { useI18n } from 'vue-i18n';
import { notify } from '@inriver/inri';
import { EditFieldMappingTabName } from '@core/enums';
import { useEditFieldMappingStore, useFunctionsStore } from '@core/stores';
import { useDsaMappingStore } from '@core/stores/useDsaMappingStore';
import { useDynamicFormatFileStore } from '@core/stores/output-adapter';
import { useDsaMappingFeature } from '@core/composables';

const editFieldMappingStore = useEditFieldMappingStore();
const functionsStore = useFunctionsStore();
const dynamicFormatFileStore = useDynamicFormatFileStore();
const dsaMappingStore = useDsaMappingStore();

// Composables
const { setScreenName } = useAppInsightsStore();
const tradingPartnerStore = useTradingPartnersStore();
const { goToPage, route } = useRouter();
const { t } = useI18n();

// Variables
const tradingPartnerId = route.params?.tradingPartnerId as string;
const mappingId = parseInt(route.params?.mappingId as string);

// Refs
const tradingPartnerName = ref('');
const leftTab = ref(EditFieldMappingTabName.FIELDS);
const rightTab = ref(EditFieldMappingTabName.MAPPING);
const { isLoading, mapping } = storeToRefs(editFieldMappingStore);
const { dynamicFormatFiles } = storeToRefs(dynamicFormatFileStore);
const isPageLoading = ref(false);

// Computed
const title = computed(() => {
  return mapping.value?.MappingName ?? '';
});

const isDynamicMapping = computed(() => route.path.includes('dynamicformats'));

const formatId = parseInt(route.params?.formatId as string);

// Use the shared composable for DSA mapping feature flag
const { isDsaMappingEnabled } = useDsaMappingFeature();

const showLoadingSpinner = computed(() => {
  return isPageLoading.value || isLoading.value;
});

// Functions
const handleDsaMappingOnSave = async (): Promise<void> => {
  if (!mapping.value || !dsaMappingStore.dsaMapping) {
    return;
  }

  const dsaHasMappedFields = dsaMappingStore.dsaMapping.MappingModelList.some((row) => !!row.inRiverFieldTypeId);
  const dsaMappingIsNew = !mapping.value.DsaMappingId;
  if (dsaMappingIsNew && !dsaHasMappedFields) {
    // If DSA mapping is new and has no mapped fields, do not save it
    return;
  }

  if (dsaMappingIsNew && dsaHasMappedFields) {
    const dsaMappingId = await dsaMappingStore.saveNewDsaMapping();
    if (!dsaMappingId) {
      console.error('failed to save DSA mapping');
      return;
    }

    mapping.value.DsaMappingId = dsaMappingId; // TODO: check if we can mapping.value.DsaMappingId directly
  }

  if (!dsaMappingIsNew && dsaHasMappedFields) {
    const dsaMappingUpdated = await dsaMappingStore.updateExistingDsaMapping();
    if (!dsaMappingUpdated) {
      console.error('failed to update DSA mapping');
      return;
    }
  }

  if (!dsaMappingIsNew && !dsaHasMappedFields && mapping.value.DsaMappingId) {
    // remove dsa mapping id from the main mapping and delete the dsa mapping
    const dsaMappingDeleted = await dsaMappingStore.deleteDsaMappingById(mapping.value.DsaMappingId);
    if (!dsaMappingDeleted) {
      console.error('failed to delete DSA mapping');
      return;
    }

    await dsaMappingStore.initializeDsaMapping(mapping.value);
    mapping.value.DsaMappingId = null;
  }
};

const unifiedSave = async () => {
  let isMappingSaved = false;
  try {
    if (isDsaMappingEnabled.value) {
      await handleDsaMappingOnSave();
    }
    // Save the main mapping
    if (isDynamicMapping.value) {
      isMappingSaved = await editFieldMappingStore.saveDynamicMappingChanges();
    } else {
      isMappingSaved = await editFieldMappingStore.saveChanges(mappingId);
    }

    if (isMappingSaved) {
      notify.success(t('core.settings.field_mapping.saved'));
    } else {
      notify.error(t('core.settings.field_mapping.warning'));
    }

    return isMappingSaved;
  } catch (error) {
    console.error('Error during unified save:', error);
    notify.error(t('core.settings.field_mapping.warning'));
    return false;
  }
};

const cancelEdit = () => {
  if (isDynamicMapping.value) {
    goToPage('dynamic-format-mappings-page', { formatId });
  } else if (!!route.params.formatId) {
    goToPage('format-mappings-page');
  } else {
    goToPage('trading-partner-settings-page');
  }
};

// Lifecycle methods
onBeforeMount(async () => {
  isPageLoading.value = true;
  if (isDynamicMapping.value) {
    setScreenName(PageName.EDIT_DYNAMIC_FIELD_MAPPING);
    tradingPartnerName.value = tradingPartnerId;
    await editFieldMappingStore.fetchDynamicMappingDetails(mappingId);
    await dynamicFormatFileStore.fetchFormatFiles();
    const formatFile = dynamicFormatFiles.value.find((file) => file.Id === formatId);
    if (formatFile) {
      const categoryId = formatFile.categoryId;
      const tradingPartnerId = formatFile.tradingPartnerId;
      await editFieldMappingStore.fetchCategoryForDynamicMapping(categoryId, tradingPartnerId);
      editFieldMappingStore.initDynamicMappingModelList();
    }
  } else {
    setScreenName(PageName.EDIT_FIELD_MAPPING);
    const isCoreTradingPartner = true;
    const tradingPartner = await tradingPartnerStore.getTradingPartnerById(tradingPartnerId, isCoreTradingPartner);
    tradingPartnerName.value = tradingPartner && tradingPartner.name;
    await editFieldMappingStore.fetchMappingDetails(mappingId);
  }

  // If DSA mapping feature is enabled, always initialize DSA mapping after regular mapping is loaded
  if (isDsaMappingEnabled.value) {
    // Wait a tick to ensure the mapping is fully reactive
    await new Promise((resolve) => setTimeout(resolve, 0));
    if (mapping.value) {
      await dsaMappingStore.initializeDsaMapping(mapping.value, mapping.value.DsaMappingId);
    }
  }

  await functionsStore.fetchAllFunctions();
  isPageLoading.value = false;
});

onUnmounted(() => {
  editFieldMappingStore.clearStore();
  // Also clear DSA mapping store to prevent memory leaks
  if (isDsaMappingEnabled.value) {
    dsaMappingStore.clearStore();
  }
});
</script>

<style lang="scss" scoped>
.spinner {
  z-index: 100;
}

.fields-functions-panel {
  min-width: 306px;
  max-height: calc(100vh - 170px);
}

.edit-mapping-tabs {
  width: 306px;
  border-right: 1px solid var(--color-grey);
  max-height: calc(100vh - 100px);
}

.right-panel {
  max-width: 80%;
  min-width: 970px;
  border-left: none;
}

.mapping-panel,
.dsa-mapping-panel {
  width: 100%;
}

:deep(.tab-panel-content .editor) {
  max-height: calc(100vh - 255px);
}

/* Tab styles */
.active-tab {
  font-weight: 600;
  background-color: #eaf6f9;
  position: relative;
}

.active-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
}

.right-tabs {
  padding-left: 16px;
}
</style>
