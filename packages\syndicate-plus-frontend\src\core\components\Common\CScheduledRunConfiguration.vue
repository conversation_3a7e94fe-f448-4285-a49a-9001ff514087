<template>
  <div>
    <q-input
      v-model="scheduleName"
      v-bind="$inri.input"
      :label="$t('core.trading_partners.scheduled_syndication.schedule_name')"
      hide-bottom-space
    />
    <div class="frequency">
      <q-radio
        v-for="frequencyType in allFrequencyTypes"
        :key="frequencyType"
        v-model="selectedFrequency"
        :val="frequencyType"
        :label="frequencyType.toLocaleLowerCase()"
        class="pt-2"
      />
    </div>
    <q-input
      v-if="isStartDateVisible"
      v-model="startDate"
      filled
      :mask="inputMask"
      :label="$t('core.trading_partners.scheduled_syndication.start_date')"
      class="pt-2"
    >
      <template #prepend>
        <q-icon name="mdi-calendar-outline" class="cursor-pointer">
          <q-popup-proxy transition-show="scale" transition-hide="scale">
            <div class="q-pa-md">
              <div class="q-gutter-md row items-start">
                <q-date v-model="startDate" :mask="dateTimeMask" />
                <q-time v-model="startDate" :mask="dateTimeMask" now-btn />
              </div>
            </div>
          </q-popup-proxy>
        </q-icon>
      </template>
    </q-input>
    <q-input
      v-if="isEndDateVisible"
      v-model="endDate"
      filled
      :mask="inputMask"
      :label="$t('core.trading_partners.scheduled_syndication.end_date')"
    >
      <template #prepend>
        <q-icon name="mdi-calendar-outline" class="cursor-pointer">
          <q-popup-proxy transition-show="scale" transition-hide="scale">
            <div class="q-pa-md">
              <div class="q-gutter-md row items-start">
                <q-date v-model="endDate" :mask="dateTimeMask" />
                <q-time v-model="endDate" :mask="dateTimeMask" now-btn />
              </div>
            </div>
          </q-popup-proxy>
        </q-icon>
      </template>
    </q-input>
    <q-btn-group v-if="isDaysVisible" class="days">
      <c-btn
        v-for="day in allDays"
        :key="day"
        :label="day.slice(0, 2).toLocaleLowerCase()"
        :color="selectedDays.includes(day) ? 'primary' : 'secondary'"
        @click="() => onDayClick(day)"
      />
    </q-btn-group>
  </div>
</template>

<script setup lang="ts">
import { Day, Frequency } from '@enums/ApiSyndication';
import { computed, ref } from 'vue';
import { allDays, allFrequencyTypes } from '@enums';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { ScheduledRunConfiguration } from '@core/interfaces';
import {
  calculateCronExpression,
  convertLocalToUtcString,
  getNextExecutionDateTime,
  isDate1PastOrEqualDate2,
  isDateTimeInFuture,
} from '@helpers/DateTimeHelpers';

dayjs.extend(utc);

// Variables
const dateTimeMask = 'YYYY/MM/DD HH:mm';
const inputMask = '####/##/## ##:##';

// Refs
const scheduleName = ref<string>();
const selectedFrequency = ref<Frequency>();
const startDate = ref<string>(dayjs().format(dateTimeMask));
const endDate = ref<string>(dayjs().format(dateTimeMask));
const selectedDays = ref<Day[]>([]);

// Computed
const isStartDateVisible = computed(() => !!selectedFrequency.value);
const isEndDateVisible = computed(() => !!selectedFrequency.value && selectedFrequency.value !== Frequency.ONCE);
const isDaysVisible = computed(() => !!selectedFrequency.value && selectedFrequency.value === Frequency.WEEKLY);
const isValidStartDate = computed(() => startDate.value && isDateTimeInFuture(startDate.value));
const isValidEndDate = computed(
  () => !endDate.value || (isDateTimeInFuture(endDate.value) && isDate1PastOrEqualDate2(endDate.value, startDate.value))
);
const isInvalid = computed(() => {
  if (!scheduleName.value) {
    return true;
  }

  switch (selectedFrequency.value) {
    case Frequency.ONCE: {
      return !isValidStartDate.value;
    }

    case Frequency.DAILY:
    case Frequency.MONTHLY:
      return !isValidStartDate.value || !isValidEndDate.value;

    case Frequency.WEEKLY: {
      return !isValidStartDate.value || !isValidEndDate.value || !selectedDays.value.length;
    }
  }

  return true;
});

// Functions
const onDayClick = (day: Day) => {
  if (selectedDays.value.includes(day)) {
    selectedDays.value = selectedDays.value.filter((x) => x !== day);
  } else {
    selectedDays.value.push(day);
  }
};

const getConfiguration = (): ScheduledRunConfiguration | undefined => {
  if (!scheduleName.value || !selectedFrequency.value) {
    return undefined;
  }

  const utcStartDate = convertLocalToUtcString(startDate.value);
  const utcEndDate = selectedFrequency.value === Frequency.ONCE ? null : convertLocalToUtcString(endDate.value);
  const cronExpression = calculateCronExpression(selectedFrequency.value, selectedDays.value, utcStartDate);
  const nextExecution = getNextExecutionDateTime(cronExpression, selectedFrequency.value, utcStartDate);

  return {
    name: scheduleName.value,
    cronExpression: cronExpression,
    nextExecution: nextExecution,
    startDate: utcStartDate,
    endDate: utcEndDate,
  };
};

defineExpose({ getConfiguration, isInvalid });
</script>

<style lang="scss" scoped>
.days {
  :deep(.q-btn) {
    padding: 4px 30px !important;
  }
}
</style>
