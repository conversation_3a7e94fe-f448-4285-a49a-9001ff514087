import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { CorePage } from '@pages/core/core.page';
import { CoreProductsTabPage } from '@pages/core/coreProductsTab.page';
import { CoreHistoryTabPage } from '@pages/core/coreHistoryTab.page';

test.describe('Products tab', () => {
  const UITestFormat = 'UITest format';
  const UITestFormat02 = 'UITest format 02';
  const UITestChannel01 = 'UI Test - Channel 01';
  const UITestNode01Channel01 = 'UI Test - Node 01 (UI Test - Channel 01)';
  const UITestNode02Channel02 = 'UI Test - Node 02 (UI Test - Channel 02)';
  const UITestEmptyNode04Channel01 = 'UI Test - empty  Node 04 (UI Test - Channel 01)';
  const UITestProduct01 = 'UI Test - Product 01 code';
  const UITestProduct02 = 'UI Test - Product 02 code';
  const UITestProduct03 = 'UI Test - Product 03 code';
  const UITestProduct04 = 'UI Test - Product 04 code';
  const imageSrc =
    'https://asset-dev4a-euw.productmarketingcloud.com/api/assetstorage/493_cc79342a-4314-4e02-9658-9bb6448a0869/SmallThumbnail/thumbnail';
  const finishedStatus = 'finished';
  const UITestProductMapping = 'UITest - ProductFormatMapping';
  const UITestCSVOutput = 'UITestCSV (csv)';
  const reviewPageTitle = 'review - UITest - ProductFormatMapping - UI Test - Node 01 (UI Test - Channel 01)';
  const entityId = '22683';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let corePage: CorePage;
  let coreProductsTabPage: CoreProductsTabPage;
  let coreHistoryTabPage: CoreHistoryTabPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    corePage = new CorePage(localPage);
    coreProductsTabPage = new CoreProductsTabPage(localPage);
    coreHistoryTabPage = new CoreHistoryTabPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.openSyndicationPage(UITestFormat);
    await corePage.productsTab.click();
  });

  test('Product image is visible', async () => {
    await coreProductsTabPage.selectChannel(UITestNode01Channel01);
    await expect(coreProductsTabPage.imagePreview(imageSrc)).toBeVisible();
    await coreProductsTabPage.selectChannel(UITestEmptyNode04Channel01);
    await expect(coreProductsTabPage.imagePreview(imageSrc)).toBeHidden();
  });

  test('Channel shows correct product', async () => {
    // Navigate from one format to the other verifying the correct products are shown.
    await coreProductsTabPage.selectChannel(UITestNode01Channel01);
    await expect(coreProductsTabPage.productName(UITestProduct01)).toBeVisible();
    await expect(coreProductsTabPage.productName(UITestProduct02)).toBeVisible();
    await expect(coreProductsTabPage.productName(UITestProduct03)).toBeHidden();
    await expect(coreProductsTabPage.productName(UITestProduct04)).toBeHidden();
    await coreProductsTabPage.goBackButton.click();
    await welcomeToSyndicatePlusPage.getCardByText(UITestFormat02).click();
    await corePage.clickProductsTab();
    await coreProductsTabPage.selectChannel(UITestNode02Channel02);
    await expect(coreProductsTabPage.productName(UITestProduct01)).toBeHidden();
    await expect(coreProductsTabPage.productName(UITestProduct02)).toBeHidden();
    await expect(coreProductsTabPage.productName(UITestProduct03)).toBeVisible();
    await expect(coreProductsTabPage.productName(UITestProduct04)).toBeVisible();
  });

  test('Syndicate single product', async () => {
    // Remove template in the history tab.
    await corePage.historyTab.click();
    // bug created https://dev.azure.com/inriver/iPMC/_workitems/edit/109199
    await coreHistoryTabPage.deleteCollectionIfExists(UITestChannel01); // If fails, change UITestChannel for UITestProduct01

    await corePage.productsTab.click();
    await coreProductsTabPage.selectChannel(UITestNode01Channel01);
    await coreProductsTabPage.productName(UITestProduct01).click();
    await coreProductsTabPage.syndicateClick();
    await coreProductsTabPage.selectMappingAndOutput(UITestProductMapping, UITestCSVOutput);
    await coreProductsTabPage.saveButton.click();

    await expect(async () => {
      await corePage.productsTab.click();
      await corePage.clickHistoryTab();
      await expect(coreHistoryTabPage.getByCollectionAndStatus(UITestChannel01, finishedStatus)).toBeVisible({
        timeout: 2000,
      });
    }).toPass(); // If fails, change UITestChannel for UITestProduct01

    // Delete the template to clear test data
    await coreHistoryTabPage.deleteCollectionIfExists(UITestChannel01);
  });

  test('Review single product', async () => {
    await coreProductsTabPage.selectChannel(UITestNode01Channel01);
    await coreProductsTabPage.productName(UITestProduct01).click();
    await coreProductsTabPage.reviewClick();
    await coreProductsTabPage.selectMapping(UITestProductMapping);
    await coreProductsTabPage.reviewDialogButton.click();

    await expect.soft(coreProductsTabPage.reviewPageTitle(reviewPageTitle)).toBeVisible();
    await expect.soft(coreProductsTabPage.reviewFilter, 'Filter button is not visible').toBeVisible();
    await expect(coreProductsTabPage.getByRoleName(entityId), 'Review table was not load correctly').toBeVisible();
  });
});
