<template>
  <c-dialog
    :model-value="true"
    class="c-dialog"
    :title="$t('core.manage_trading_partners.duplicate_mapping_dialog.title')"
    :is-loading="isLoading"
    :confirm-button-text="$t('syndicate_plus.common.save')"
    :disable-confirm="confirmButtonIsDisabled"
    @cancel="onCancel"
    @confirm="onSave"
  >
    <q-form ref="form" greedy autofocus>
      <div class="row q-gutter-lg">
        <div class="col section">
          <q-input
            v-model="mappingName"
            v-bind="$inri.input"
            :label="$t('core.manage_trading_partners.duplicate_mapping_dialog.mapping_name')"
            :rules="[$validate.required()]"
            autofocus
          />
        </div>
      </div>
    </q-form>
  </c-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// Props
interface Props {
  originalMappingName: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'duplicate-mapping': [mappingName: string];
  cancel: [];
}>();

// Refs
const isLoading = ref(false);
const mappingName = ref(props.originalMappingName);
const form = ref();

// Computed
const confirmButtonIsDisabled = computed(() => {
  return !mappingName.value?.trim();
});

// Functions
const onSave = async () => {
  const isValid = await form.value?.validate();
  if (!isValid) {
    return;
  }

  emit('duplicate-mapping', mappingName.value.trim());
};

const onCancel = () => {
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.section {
  h3 {
    padding-bottom: 20px;
    font-weight: normal;
  }
}
</style>
