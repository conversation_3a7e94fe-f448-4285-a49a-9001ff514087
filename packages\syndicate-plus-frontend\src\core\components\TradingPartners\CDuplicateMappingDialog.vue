<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    :confirm-button-text="$t('syndicate_plus.common.save')"
    :disable-confirm="confirmButtonIsDisabled"
    @cancel="cancel"
    @confirm="onSave"
  >
    <q-form ref="form" greedy autofocus>
      <div class="row q-gutter-lg">
        <div class="col section">
          <h3>
            {{ $t('core.manage_trading_partners.duplicate_mapping_dialog.title') }}
          </h3>
          <q-input
            v-model="mappingName"
            v-bind="$inri.input"
            :label="$t('core.manage_trading_partners.duplicate_mapping_dialog.mapping_name')"
            :rules="[$validate.required()]"
            autofocus
          />
        </div>
      </div>
    </q-form>
  </c-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useDialog } from '@inriver/inri';
import { useI18n } from 'vue-i18n';

// Props
interface Props {
  originalMappingName: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'duplicate-mapping': [mappingName: string];
}>();

// Composables
const { showDialog, cancel, confirmSuccess } = useDialog();
const { t } = useI18n();

// Refs
const isLoading = ref(false);
const mappingName = ref(props.originalMappingName);
const form = ref();

// Computed
const confirmButtonIsDisabled = computed(() => {
  return !mappingName.value?.trim();
});

// Functions
const onSave = async () => {
  const isValid = await form.value?.validate();
  if (!isValid) {
    return;
  }

  isLoading.value = true;
  try {
    emit('duplicate-mapping', mappingName.value.trim());
    confirmSuccess(null);
  } catch (error) {
    console.error('Error duplicating mapping:', error);
  } finally {
    isLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.section {
  h3 {
    padding-bottom: 20px;
    font-weight: normal;
  }
}
</style>
