import { NewOutput, OutputCSVSettings, OutputExcelSettings, WorksheetPair } from '@core/interfaces/Outputs';
import { computed, ref } from 'vue';
import { OutputType, DeliveryMethod, DataLevel, ExcelOutputMode } from '@core/enums';
import { saveOutput } from '@core/services/Outputs';

export default function useOutputSettings() {
  // Variables
  const delimiter = ';';
  const dataLevel = DataLevel.BASIC_DATA;
  const templateFileName = 'MyTemplateFileName.xlsx';
  const exportFileName = 'MyOutputFileName.xlsx';
  const worksheetName = 'SheetName1';
  const encodingToUTF8 = false;
  let exportToZipFolder = false;

  // Refs
  const newOutput = ref<NewOutput>({
    OutputName: '',
    OutputType: OutputType.XML,
    Settings: {
      EnableCompression: false,
      OutputFormat: OutputType.XML,
      DeliveryMethods: '',
      DeliveryMethodsList: [],
      Azure_FileName: 'YourFileName.xml',
      Azure_ConnectionString:
        'DefaultEndpointsProtocol=https;AccountName=youraccountname;AccountKey=youraccountkey;EndpointSuffix=core.windows.net',
      Azure_Container: 'yourcontainer',
      Azure_Path: '/folder/subfolder',
      FTP_FileName: 'YourFileName.xml',
      FTP_Host: 'ftp://ftp.yourftpserver.com',
      FTP_UserName: '<EMAIL>',
      FTP_Path: '/folder/subfolder',
      FTP_Port: '21',
      FTPS_FileName: 'YourFileName.xml',
      FTPS_Host: 'ftps://ftps.yourftpsserver.com',
      FTPS_UserName: '<EMAIL>',
      FTPS_Path: '/folder/subfolder',
      FTPS_Port: '21',
      Http_Filename: 'YourFileName.xml',
      Http_Url: 'https://yourserver.com/yourwebhook/',
      SFTP_FileName: 'YourFileName.xml',
      SFTP_Host: 'sftp://sftp.yoursftpserver.com',
      SFTP_UserName: '<EMAIL>',
      SFTP_Path: '/folder/subfolder',
      SFTP_Port: '22',
    },
  });
  const worksheetStartCell = ref<string>('A10');
  const worksheetPairs = ref<WorksheetPair[]>([
    {
      worksheetName: 'SheetName1',
      startCell: 'A10',
    },
  ]);

  // Computed
  const isOutputFormatDisabled = computed(() => newOutput.value.Settings.EnableCompression);
  const isCSVOutput = computed(() => newOutput.value.OutputType === OutputType.CSV);
  const isExcelOutput = computed(() => newOutput.value.OutputType === OutputType.EXCEL);
  const isAzureDelivery = computed(() =>
    newOutput.value.Settings.DeliveryMethodsList?.includes(DeliveryMethod.AZURE_BLOB)
  );
  const isFTPDelivery = computed(() => newOutput.value.Settings.DeliveryMethodsList?.includes(DeliveryMethod.FTP));
  const isFTPSDelivery = computed(() => newOutput.value.Settings.DeliveryMethodsList?.includes(DeliveryMethod.FTPS));
  const isHTTPDelivery = computed(() =>
    newOutput.value.Settings.DeliveryMethodsList?.includes(DeliveryMethod.HTTP_POST)
  );
  const isSFTPDelivery = computed(() => newOutput.value.Settings.DeliveryMethodsList?.includes(DeliveryMethod.SFTP));
  const areAllPairsValid = computed(() => {
    return worksheetPairs.value.every(
      (pair) =>
        pair.worksheetName.trim() !== '' && pair.startCell.trim() !== '' && /^[A-Z]+[0-9]+$/.test(pair.startCell)
    );
  });
  const isMultiTabMode = computed(() => worksheetPairs.value.length > 1);
  const firstWorksheetName = computed(() => {
    return worksheetPairs.value.length > 0 ? worksheetPairs.value[0].worksheetName : '';
  });

  // Functions
  const setDeliveryMethodsFromList = () => {
    cleanUnusedDefaultDeliverySettings();
    newOutput.value.Settings.DeliveryMethods = newOutput.value.Settings.DeliveryMethodsList?.join(';') ?? '';
    newOutput.value.Settings.DeliveryMethodsList = undefined;
  };

  const cleanUnusedDefaultDeliverySettings = () => {
    if (!isAzureDelivery.value) {
      newOutput.value.Settings.Azure_FileName = undefined;
      newOutput.value.Settings.Azure_ConnectionString = undefined;
      newOutput.value.Settings.Azure_Container = undefined;
      newOutput.value.Settings.Azure_Path = undefined;
    }
    if (!isFTPDelivery.value) {
      newOutput.value.Settings.FTP_FileName = undefined;
      newOutput.value.Settings.FTP_Host = undefined;
      newOutput.value.Settings.FTP_UserName = undefined;
      newOutput.value.Settings.FTP_Path = undefined;
      newOutput.value.Settings.FTP_Port = undefined;
    }
    if (!isFTPSDelivery.value) {
      newOutput.value.Settings.FTPS_FileName = undefined;
      newOutput.value.Settings.FTPS_Host = undefined;
      newOutput.value.Settings.FTPS_UserName = undefined;
      newOutput.value.Settings.FTPS_Path = undefined;
      newOutput.value.Settings.FTPS_Port = undefined;
    }
    if (!isHTTPDelivery.value) {
      newOutput.value.Settings.Http_Filename = undefined;
      newOutput.value.Settings.Http_Url = undefined;
    }
    if (!isSFTPDelivery.value) {
      newOutput.value.Settings.SFTP_FileName = undefined;
      newOutput.value.Settings.SFTP_Host = undefined;
      newOutput.value.Settings.SFTP_UserName = undefined;
      newOutput.value.Settings.SFTP_Path = undefined;
      newOutput.value.Settings.SFTP_Port = undefined;
    }
  };

  const addWorksheetPair = () => {
    worksheetPairs.value.push({
      worksheetName: '',
      startCell: 'A10',
    });
  };

  const removeWorksheetPair = (index: number) => {
    // Don't remove if it's the only pair left
    if (worksheetPairs.value.length > 1) {
      worksheetPairs.value.splice(index, 1);
    }
  };

  const setMultiTabWorksheetStartCells = () => {
    const worksheetStartCells: Record<string, string> = {};

    // Add all worksheets from the pairs array
    worksheetPairs.value.forEach((pair) => {
      if (pair.worksheetName && pair.startCell) {
        worksheetStartCells[pair.worksheetName] = pair.startCell;
      }
    });

    const multiTabWorksheetStartCells = JSON.stringify(worksheetStartCells);
    (newOutput.value.Settings as OutputExcelSettings).MultiTabWorksheetStartCells = multiTabWorksheetStartCells;

    // Set the ExportExcelWorksheetsName to the first worksheet name
    (newOutput.value.Settings as OutputExcelSettings).ExportExcelWorksheetsName = firstWorksheetName.value;

    // Set the appropriate mode based on number of worksheets
    (newOutput.value.Settings as OutputExcelSettings).Mode = isMultiTabMode.value
      ? ExcelOutputMode.MULTI_TAB_BASIC
      : ExcelOutputMode.SINGLE_TAB;
  };

  const saveNewOutput = async () => {
    if (!newOutput.value) {
      return;
    }

    setDeliveryMethodsFromList();
    isExcelOutput.value && setMultiTabWorksheetStartCells();
    try {
      const isSaved = await saveOutput(newOutput.value);
      return isSaved;
    } catch (error) {
      return false;
    }
  };

  const enableCompressionIsChanged = () => {
    if (!newOutput.value.Settings.EnableCompression) {
      newOutput.value.Settings.OutputFormat = newOutput.value.OutputType;
      return;
    }

    newOutput.value.Settings.OutputFormat = OutputType.ZIP;
    exportToZipFolder = true;

    if ('UseZip' in newOutput.value.Settings) {
      (newOutput.value.Settings as OutputExcelSettings).UseZip = exportToZipFolder;
    }
  };

  const onOutputTypeChange = () => {
    if (!newOutput.value.Settings.EnableCompression) {
      newOutput.value.Settings.OutputFormat = newOutput.value.OutputType;
    }

    if (isCSVOutput.value) {
      (newOutput.value.Settings as OutputCSVSettings).Delimiter = delimiter;
      (newOutput.value.Settings as OutputCSVSettings).DataLevel = dataLevel;
    }

    if (!isCSVOutput.value) {
      delete (newOutput.value.Settings as any).Delimiter;
      delete (newOutput.value.Settings as any).DataLevel;
    }

    if (isExcelOutput.value) {
      (newOutput.value.Settings as OutputExcelSettings).TemplateFilename = templateFileName;
      (newOutput.value.Settings as OutputExcelSettings).ExportFilename = exportFileName;
      (newOutput.value.Settings as OutputExcelSettings).UseZip = exportToZipFolder;
      (newOutput.value.Settings as OutputExcelSettings).UseUTF8 = encodingToUTF8;
      (newOutput.value.Settings as OutputExcelSettings).Mode = ExcelOutputMode.SINGLE_TAB;
      (newOutput.value.Settings as OutputExcelSettings).ExportExcelWorksheetsName = worksheetName;
      (newOutput.value.Settings as OutputExcelSettings).MultiTabWorksheetStartCells = '';

      // Initialize with one worksheet pair if empty
      if (worksheetPairs.value.length === 0) {
        worksheetPairs.value = [
          {
            worksheetName: worksheetName,
            startCell: 'A10',
          },
        ];
      }
    }

    if (!isExcelOutput.value) {
      delete (newOutput.value.Settings as any).TemplateFilename;
      delete (newOutput.value.Settings as any).ExportFilename;
      delete (newOutput.value.Settings as any).UseZip;
      delete (newOutput.value.Settings as any).UseUTF8;
      delete (newOutput.value.Settings as any).Mode;
      delete (newOutput.value.Settings as any).ExportExcelWorksheetsName;
      delete (newOutput.value.Settings as any).MultiTabWorksheetStartCells;
    }
  };

  return {
    newOutput,
    isOutputFormatDisabled,
    isCSVOutput,
    isExcelOutput,
    isAzureDelivery,
    isFTPDelivery,
    isFTPSDelivery,
    isHTTPDelivery,
    isSFTPDelivery,
    worksheetStartCell,
    worksheetPairs,
    areAllPairsValid,
    isMultiTabMode,
    firstWorksheetName,
    addWorksheetPair,
    removeWorksheetPair,
    saveNewOutput,
    enableCompressionIsChanged,
    setMultiTabWorksheetStartCells,
    onOutputTypeChange,
    setDeliveryMethodsFromList,
  };
}
