import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { WelcomeSettingsPage } from '@pages/welcomePage/welcomeSettings/welcomeSettings.page';
import { FormatsTabPage } from '@pages/welcomePage/welcomeSettings/formatsTab/formatsTab.page';
import { AssignOutputsPage } from '@pages/welcomePage/welcomeSettings/formatsTab/assignOutputs.page';

test.describe('Assign collections', () => {
  const output = 'UITestAddRemoveTest (xml) ';
  const formatSpeakers = 'best-buy - Speakers';
  const UITestFormat = 'UITest format - add and remove output';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let welcomeSettingsPage: WelcomeSettingsPage;
  let formatTabPage: FormatsTabPage;
  let assingOutputsPage: AssignOutputsPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    welcomeSettingsPage = new WelcomeSettingsPage(localPage);
    formatTabPage = new FormatsTabPage(localPage);
    assingOutputsPage = new AssignOutputsPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();
    await welcomeSettingsPage.formatsTab.click();
    await expect(formatTabPage.formatsTable, 'Formats Table is not visible').toBeVisible();
  });

  test('Add and remove an output from an API format', async () => {
    await formatTabPage.selectFormat(formatSpeakers).click();
    await formatTabPage.assignOutputButton.click();
    await assingOutputsPage.waitForPageToLoad();

    // add output
    await expect(async () => {
      await assingOutputsPage.dragAndDropOutput(output);
      await expect(assingOutputsPage.addedOutput(output)).toBeVisible({ timeout: 2000 });
    }).toPass();

    await assingOutputsPage.saveButton.click();
    await assingOutputsPage.goBackButton.click();
    await formatTabPage.selectFormat(formatSpeakers).click();
    await formatTabPage.assignOutputButton.click();
    await assingOutputsPage.waitForPageToLoad();
    await expect.soft(assingOutputsPage.addedOutput(output)).toBeVisible();

    // remove output
    await assingOutputsPage.removeOutputLink(output);
    await assingOutputsPage.saveButton.click();
    await assingOutputsPage.goBackButton.click();
    await formatTabPage.selectFormat(formatSpeakers).click();
    await formatTabPage.assignCollectionButton.click();
    await assingOutputsPage.waitForPageToLoad();
    await expect(assingOutputsPage.addedOutput(output)).not.toBeVisible();
  });

  test('Add and remove an output from a format file', async () => {
    await formatTabPage.selectFormat(UITestFormat).click();
    await formatTabPage.assignOutputButton.click();
    await assingOutputsPage.waitForPageToLoad();

    // add output
    await expect(async () => {
      await assingOutputsPage.dragAndDropOutput(output);
      await expect(assingOutputsPage.addedOutput(output)).toBeVisible({ timeout: 2000 });
    }).toPass();

    await assingOutputsPage.saveButton.click();
    await assingOutputsPage.goBackButton.click();
    await formatTabPage.selectFormat(UITestFormat).click();
    await formatTabPage.assignOutputButton.click();
    await assingOutputsPage.waitForPageToLoad();
    await expect.soft(assingOutputsPage.addedOutput(output)).toBeVisible();

    // remove output
    await assingOutputsPage.removeOutputLink(output);
    await assingOutputsPage.saveButton.click();
    await assingOutputsPage.goBackButton.click();
    await formatTabPage.selectFormat(UITestFormat).click();
    await formatTabPage.assignCollectionButton.click();
    await assingOutputsPage.waitForPageToLoad();
    await expect(assingOutputsPage.addedOutput(output)).not.toBeVisible();
  });
});
