import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ref } from 'vue';
import useDuplicateMapping from './useDuplicateMapping';
import { Mapping, DynamicMappingResponse, MappingDetailsResponse, NewMapping } from '@core/interfaces';

// Mock dependencies
vi.mock('@inriver/inri', () => ({
  notify: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key: string) => key),
  }),
}));

vi.mock('@core/services/Mappings/mappingsApi', () => ({
  getMapping: vi.fn(),
  saveMapping: vi.fn(),
}));

vi.mock('@core/services/Mappings/dynamicMappingsApi', () => ({
  fetchDynamicMappingById: vi.fn(),
  saveDynamicMapping: vi.fn(),
}));

import { notify } from '@inriver/inri';
import { getMapping, saveMapping } from '@core/services/Mappings/mappingsApi';
import { fetchDynamicMappingById, saveDynamicMapping } from '@core/services/Mappings/dynamicMappingsApi';

describe('useDuplicateMapping', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('duplicateMapping', () => {
    it('should successfully duplicate a regular mapping', async () => {
      // Arrange
      const { duplicateMapping } = useDuplicateMapping();
      
      const originalMapping: Mapping = {
        MappingId: 1,
        MappingName: 'Original Mapping',
        FormatFileId: 123,
        EnableSKU: true,
        DsaMappingId: null,
      };

      const mappingDetails: MappingDetailsResponse = {
        MappingId: 1,
        MappingName: 'Original Mapping',
        WorkareaEntityTypeId: 'Product',
        OutputEntityTypeId: 'Product',
        FirstRelatedEntityTypeId: 'Brand',
        FirstLinkEntityTypeId: null,
        SecondRelatedEntityTypeId: 'Category',
        SecondLinkEntityTypeId: null,
        DefaultLanguage: 'en',
        MappingModelList: [],
        ResourceFields: [],
        FormatId: 123,
        EnableSKU: true,
        DsaMappingId: null,
      };

      const newMappingName = 'Duplicated Mapping';
      const savedMappingId = 2;

      (getMapping as any).mockResolvedValue(mappingDetails);
      (saveMapping as any).mockResolvedValue(savedMappingId);

      // Act
      const result = await duplicateMapping(originalMapping, newMappingName, false);

      // Assert
      expect(result).toBe(true);
      expect(getMapping).toHaveBeenCalledWith(1);
      expect(saveMapping).toHaveBeenCalledWith(
        expect.objectContaining({
          MappingId: null,
          MappingName: newMappingName,
          WorkareaEntityTypeId: 'Product',
          OutputEntityTypeId: 'Product',
          EnableSKU: true,
          FormatId: 123,
          DsaMappingId: null,
        })
      );
      expect(notify.success).toHaveBeenCalledWith('core.manage_trading_partners.duplicate_mapping_dialog.save_success');
    });

    it('should successfully duplicate a dynamic mapping', async () => {
      // Arrange
      const { duplicateMapping } = useDuplicateMapping();
      
      const originalMapping: DynamicMappingResponse = {
        id: 1,
        name: 'Original Dynamic Mapping',
        data: JSON.stringify({
          MappingId: 1,
          MappingName: 'Original Dynamic Mapping',
          WorkareaEntityTypeId: 'Product',
          OutputEntityTypeId: 'Product',
          EnableSKU: false,
          FormatId: 456,
        }),
        createdBy: '<EMAIL>',
        updatedBy: '',
        tradingPartnerId: 'tp1',
        category: 'category1',
        createdDate: '2023-01-01',
        updatedDate: '',
        environmentFormatId: '456',
        enableSKU: false,
      };

      const newMappingName = 'Duplicated Dynamic Mapping';
      const savedMapping = { id: 2, name: newMappingName };

      (fetchDynamicMappingById as any).mockResolvedValue(originalMapping);
      (saveDynamicMapping as any).mockResolvedValue(savedMapping);

      // Act
      const result = await duplicateMapping(originalMapping, newMappingName, true);

      // Assert
      expect(result).toBe(true);
      expect(fetchDynamicMappingById).toHaveBeenCalledWith(1);
      expect(saveDynamicMapping).toHaveBeenCalledWith(
        456,
        expect.objectContaining({
          MappingId: null,
          MappingName: newMappingName,
          WorkareaEntityTypeId: 'Product',
          OutputEntityTypeId: 'Product',
          EnableSKU: false,
          FormatId: 456,
          DsaMappingId: null,
        })
      );
      expect(notify.success).toHaveBeenCalledWith('core.manage_trading_partners.duplicate_mapping_dialog.save_success');
    });

    it('should handle error when fetching regular mapping fails', async () => {
      // Arrange
      const { duplicateMapping } = useDuplicateMapping();
      
      const originalMapping: Mapping = {
        MappingId: 1,
        MappingName: 'Original Mapping',
        FormatFileId: 123,
        EnableSKU: true,
        DsaMappingId: null,
      };

      (getMapping as any).mockResolvedValue(undefined);

      // Act
      const result = await duplicateMapping(originalMapping, 'New Name', false);

      // Assert
      expect(result).toBe(false);
      expect(notify.error).toHaveBeenCalledWith('core.manage_trading_partners.duplicate_mapping_dialog.save_error');
      expect(saveMapping).not.toHaveBeenCalled();
    });

    it('should handle error when saving regular mapping fails', async () => {
      // Arrange
      const { duplicateMapping } = useDuplicateMapping();
      
      const originalMapping: Mapping = {
        MappingId: 1,
        MappingName: 'Original Mapping',
        FormatFileId: 123,
        EnableSKU: true,
        DsaMappingId: null,
      };

      const mappingDetails: MappingDetailsResponse = {
        MappingId: 1,
        MappingName: 'Original Mapping',
        WorkareaEntityTypeId: 'Product',
        OutputEntityTypeId: 'Product',
        FirstRelatedEntityTypeId: 'Brand',
        FirstLinkEntityTypeId: null,
        SecondRelatedEntityTypeId: 'Category',
        SecondLinkEntityTypeId: null,
        DefaultLanguage: 'en',
        MappingModelList: [],
        ResourceFields: [],
        FormatId: 123,
        EnableSKU: true,
        DsaMappingId: null,
      };

      (getMapping as any).mockResolvedValue(mappingDetails);
      (saveMapping as any).mockResolvedValue(undefined);

      // Act
      const result = await duplicateMapping(originalMapping, 'New Name', false);

      // Assert
      expect(result).toBe(false);
      expect(notify.error).toHaveBeenCalledWith('core.manage_trading_partners.duplicate_mapping_dialog.save_error');
    });

    it('should handle error when fetching dynamic mapping fails', async () => {
      // Arrange
      const { duplicateMapping } = useDuplicateMapping();
      
      const originalMapping: DynamicMappingResponse = {
        id: 1,
        name: 'Original Dynamic Mapping',
        data: '',
        createdBy: '<EMAIL>',
        updatedBy: '',
        tradingPartnerId: 'tp1',
        category: 'category1',
        createdDate: '2023-01-01',
        updatedDate: '',
        environmentFormatId: '456',
        enableSKU: false,
      };

      (fetchDynamicMappingById as any).mockResolvedValue(null);

      // Act
      const result = await duplicateMapping(originalMapping, 'New Name', true);

      // Assert
      expect(result).toBe(false);
      expect(notify.error).toHaveBeenCalledWith('core.manage_trading_partners.duplicate_mapping_dialog.save_error');
      expect(saveDynamicMapping).not.toHaveBeenCalled();
    });

    it('should set isLoading correctly during operation', async () => {
      // Arrange
      const { duplicateMapping, isLoading } = useDuplicateMapping();
      
      const originalMapping: Mapping = {
        MappingId: 1,
        MappingName: 'Original Mapping',
        FormatFileId: 123,
        EnableSKU: true,
        DsaMappingId: null,
      };

      const mappingDetails: MappingDetailsResponse = {
        MappingId: 1,
        MappingName: 'Original Mapping',
        WorkareaEntityTypeId: 'Product',
        OutputEntityTypeId: 'Product',
        FirstRelatedEntityTypeId: 'Brand',
        FirstLinkEntityTypeId: null,
        SecondRelatedEntityTypeId: 'Category',
        SecondLinkEntityTypeId: null,
        DefaultLanguage: 'en',
        MappingModelList: [],
        ResourceFields: [],
        FormatId: 123,
        EnableSKU: true,
        DsaMappingId: null,
      };

      (getMapping as any).mockResolvedValue(mappingDetails);
      (saveMapping as any).mockResolvedValue(2);

      // Act & Assert
      expect(isLoading.value).toBe(false);
      
      const promise = duplicateMapping(originalMapping, 'New Name', false);
      
      // isLoading should be true during operation
      expect(isLoading.value).toBe(true);
      
      await promise;
      
      // isLoading should be false after operation
      expect(isLoading.value).toBe(false);
    });
  });
});
