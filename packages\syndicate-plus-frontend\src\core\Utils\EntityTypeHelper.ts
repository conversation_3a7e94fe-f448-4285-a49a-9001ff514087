import { EntityType } from '@core/enums/EntityType';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';
import LocalStorageService from '@services/LocalStorageService';

/**
 * Determines the entity type to use based on settings and environment
 * Checks for custom entity type setting first, then falls back to environment-based logic
 * @returns The entity type string to use (from EntityType enum)
 */
export function getEntityTypeForEnvironment(): string {
  try {
    // First check if we have a custom setting for entity type
    const customEntityType = LocalStorageService.getCustomEntityType();

    // Use any custom entity type directly if it exists
    if (customEntityType) {
      return customEntityType;
    }

    // Fall back to environment-based logic
    const envGlobalId = LocalStorageService.getEnvironmentGlobalId();
    if (envGlobalId) {
      return envGlobalId === 'prod-marshallgroup-prod' ? EntityType.ITEM : EntityType.PRODUCT;
    }
    const environmentGid = getEnvironmentGlobalId();
    return environmentGid === 'prod-marshallgroup-prod' ? EntityType.ITEM : EntityType.PRODUCT;
  } catch (error) {
    console.error('Error determining entity type:', error);
    return EntityType.PRODUCT;
  }
}
