import { ref, watch, type Ref } from 'vue';
import type { DataSubmissionEntity } from '@core/interfaces';

export interface ParsedApiFailureResponse {
  [field: string]: Array<{
    Level: string;
    Message: string;
    MessageCode: string;
  }>;
}

export function useApiFailureResponse(entity: Ref<DataSubmissionEntity | null>) {
  const parsedResponse = ref<ParsedApiFailureResponse | null>(null);

  const parseApiFailureResponse = (apiFailureResponse: string): ParsedApiFailureResponse => {
    try {
      return JSON.parse(apiFailureResponse);
    } catch (error) {
      return {
        'Error Message': [
          {
            Level: 'error',
            Message: apiFailureResponse,
            MessageCode: 'n/a',
          },
        ],
      };
    }
  };

  const updateParsedResponse = (newEntity: DataSubmissionEntity | null) => {
    if (newEntity?.apiFailureResponse) {
      parsedResponse.value = parseApiFailureResponse(newEntity.apiFailureResponse);
    } else {
      parsedResponse.value = null;
    }
  };

  // Watch for entity changes
  watch(
    entity,
    (newEntity) => {
      updateParsedResponse(newEntity);
    },
    { immediate: true }
  );

  return {
    parsedResponse,
    parseApiFailureResponse,
    updateParsedResponse,
  };
}
