import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ref, computed } from 'vue';
import { useFieldMappingButtons, MappingPanelType, type UseFieldMappingButtonsOptions } from './useFieldMappingButtons';
import { checkIfFunctionRequiresConfiguration, checkIfIsCustomFunction } from '@core/Utils';

// Mock dependencies
vi.mock('@inriver/inri', () => ({
  notify: {
    error: vi.fn(),
  },
}));

vi.mock('vue-i18n', () => ({
  useI18n: () => ({
    t: vi.fn((key: string) => key),
  }),
}));

vi.mock('@core/services/Mappings/utils', () => ({
  isMapped: vi.fn((row: any) => !!row.inRiverFieldTypeId),
}));

vi.mock('@core/Utils', () => ({
  checkIfFunctionRequiresConfiguration: vi.fn(),
  checkIfIsCustomFunction: vi.fn(),
}));

describe('useFieldMappingButtons', () => {
  let mockOptions: UseFieldMappingButtonsOptions;
  let mockHandlers: {
    saveHandler: ReturnType<typeof vi.fn>;
    cancelEditHandler: ReturnType<typeof vi.fn>;
    unmapSourceFieldHandler: ReturnType<typeof vi.fn>;
    unmapFunctionHandler: ReturnType<typeof vi.fn>;
    mapAutomaticallyHandler: ReturnType<typeof vi.fn>;
    openDefaultLanguageDialogHandler: ReturnType<typeof vi.fn>;
    importMappingHandler: ReturnType<typeof vi.fn>;
    exportMappingHandler: ReturnType<typeof vi.fn>;
    openFunctionSettingsHandler: ReturnType<typeof vi.fn>;
    openMapEnumDialogHandler: ReturnType<typeof vi.fn>;
    addListItemHandler: ReturnType<typeof vi.fn>;
    removeListItemHandler: ReturnType<typeof vi.fn>;
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Create mock handlers
    mockHandlers = {
      saveHandler: vi.fn().mockResolvedValue(undefined),
      cancelEditHandler: vi.fn(),
      unmapSourceFieldHandler: vi.fn(),
      unmapFunctionHandler: vi.fn(),
      mapAutomaticallyHandler: vi.fn(),
      openDefaultLanguageDialogHandler: vi.fn(),
      importMappingHandler: vi.fn().mockResolvedValue(undefined),
      exportMappingHandler: vi.fn().mockResolvedValue(undefined),
      openFunctionSettingsHandler: vi.fn(),
      openMapEnumDialogHandler: vi.fn(),
      addListItemHandler: vi.fn(),
      removeListItemHandler: vi.fn(),
    };

    // Default mock options
    mockOptions = {
      panelType: MappingPanelType.Standard,
      selectedRows: ref([]),
      selectedResourceRows: ref([]),
      displayedRows: computed(() => []),
      isDynamicMapping: computed(() => false),
      selectedRowIsObjectType: computed(() => false),
      isMapEnumButtonVisibleFn: computed(() => false),
      ...mockHandlers,
    } as UseFieldMappingButtonsOptions;
  });

  describe('Button visibility - Standard panel', () => {
    beforeEach(() => {
      mockOptions.panelType = MappingPanelType.Standard;
    });

    describe('isUnmapSourceFieldButtonVisible', () => {
      it('should be visible when selectedRows has mapped fields', () => {
        mockOptions.selectedRows.value = [{ inRiverFieldTypeId: '123' }];
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(true);
      });

      it('should be visible when selectedResourceRows has mapped fields', () => {
        if (mockOptions.selectedResourceRows) {
          mockOptions.selectedResourceRows.value = [{ inRiverFieldTypeId: '456' }];
        }
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(true);
      });

      it('should be hidden when no mapped fields exist', () => {
        mockOptions.selectedRows.value = [{ inRiverFieldTypeId: null }];
        if (mockOptions.selectedResourceRows) {
          mockOptions.selectedResourceRows.value = [];
        }
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(false);
      });

      it('should be hidden when selectedRows is empty', () => {
        mockOptions.selectedRows.value = [];
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(false);
      });
    });

    describe('isUnmapFunctionButtonVisible', () => {
      it('should be visible when selectedRows has ConverterArgs', () => {
        mockOptions.selectedRows.value = [{ ConverterArgs: '{}' }];
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapFunctionButtonVisible.value).toBe(true);
      });

      it('should be visible when selectedResourceRows has ConverterArgs', () => {
        if (mockOptions.selectedResourceRows) {
          mockOptions.selectedResourceRows.value = [{ ConverterArgs: '{}' }];
        }
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapFunctionButtonVisible.value).toBe(true);
      });

      it('should be hidden when no ConverterArgs exist', () => {
        mockOptions.selectedRows.value = [{}];
        if (mockOptions.selectedResourceRows) {
          mockOptions.selectedResourceRows.value = [{}];
        }
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapFunctionButtonVisible.value).toBe(false);
      });
    });

    describe('isEditFunctionSettingsButtonVisible', () => {
      it('should be hidden when multiple rows selected', () => {
        mockOptions.selectedRows.value = [{}, {}];
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(false);
      });

      it('should be hidden when no ConverterArgs', () => {
        mockOptions.selectedRows.value = [{}];
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(false);
      });

      it('should be visible when function requires configuration', () => {
        const converterArgs = JSON.stringify({
          transformations: [{ function: { name: 'TestFunction' } }],
        });
        mockOptions.selectedRows.value = [{ ConverterArgs: converterArgs }];

        vi.mocked(checkIfIsCustomFunction).mockReturnValue(false);
        vi.mocked(checkIfFunctionRequiresConfiguration).mockReturnValue(true);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(true);
      });

      it('should be visible when function is custom', () => {
        const converterArgs = JSON.stringify({
          transformations: [{ function: { name: 'CustomFunction' } }],
        });
        mockOptions.selectedRows.value = [{ ConverterArgs: converterArgs }];

        vi.mocked(checkIfIsCustomFunction).mockReturnValue(true);
        vi.mocked(checkIfFunctionRequiresConfiguration).mockReturnValue(false);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(true);
      });

      it('should be hidden when function does not require configuration and is not custom', () => {
        const converterArgs = JSON.stringify({
          transformations: [{ function: { name: 'SimpleFunction' } }],
        });
        mockOptions.selectedRows.value = [{ ConverterArgs: converterArgs }];

        vi.mocked(checkIfIsCustomFunction).mockReturnValue(false);
        vi.mocked(checkIfFunctionRequiresConfiguration).mockReturnValue(false);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(false);
      });

      it('should handle invalid JSON in ConverterArgs gracefully', () => {
        mockOptions.selectedRows.value = [{ ConverterArgs: 'invalid json {' }];

        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => undefined);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Error parsing ConverterArgs:', expect.any(SyntaxError));

        consoleSpy.mockRestore();
      });
    });

    describe('isAddListItemButtonVisible', () => {
      it('should be visible when dynamic mapping, object type, and one row selected', () => {
        mockOptions.selectedRows.value = [{}];
        mockOptions.isDynamicMapping = computed(() => true);
        mockOptions.selectedRowIsObjectType = computed(() => true);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isAddListItemButtonVisible.value).toBe(true);
      });

      it('should be hidden when not dynamic mapping', () => {
        mockOptions.selectedRows.value = [{}];
        mockOptions.isDynamicMapping = computed(() => false);
        mockOptions.selectedRowIsObjectType = computed(() => true);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isAddListItemButtonVisible.value).toBe(false);
      });

      it('should be hidden when not object type', () => {
        mockOptions.selectedRows.value = [{}];
        mockOptions.isDynamicMapping = computed(() => true);
        mockOptions.selectedRowIsObjectType = computed(() => false);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isAddListItemButtonVisible.value).toBe(false);
      });

      it('should be hidden when multiple rows selected', () => {
        mockOptions.selectedRows.value = [{}, {}];
        mockOptions.isDynamicMapping = computed(() => true);
        mockOptions.selectedRowIsObjectType = computed(() => true);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isAddListItemButtonVisible.value).toBe(false);
      });
    });

    describe('isRemoveListItemButtonVisible', () => {
      it('should be visible when conditions are met and list items exist', () => {
        const selectedRow = { FormatField: 'testField' };
        mockOptions.selectedRows.value = [selectedRow];
        mockOptions.isDynamicMapping = computed(() => true);
        mockOptions.selectedRowIsObjectType = computed(() => true);
        mockOptions.displayedRows = computed(() => [{ listItemOf: 'testField' }, { listItemOf: 'otherField' }]);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isRemoveListItemButtonVisible.value).toBe(true);
      });

      it('should be hidden when no list items exist', () => {
        const selectedRow = { FormatField: 'testField' };
        mockOptions.selectedRows.value = [selectedRow];
        mockOptions.isDynamicMapping = computed(() => true);
        mockOptions.selectedRowIsObjectType = computed(() => true);
        mockOptions.displayedRows = computed(() => []);

        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isRemoveListItemButtonVisible.value).toBe(false);
      });
    });

    describe('Standard panel specific buttons', () => {
      it('should show standard panel buttons', () => {
        const { visibility } = useFieldMappingButtons(mockOptions);

        expect(visibility.isAutomapButtonVisible.value).toBe(true);
        expect(visibility.isExportMappingButtonVisible.value).toBe(true);
        expect(visibility.isImportMappingButtonVisible.value).toBe(true);
        expect(visibility.isSetDefaultLanguageButtonVisible.value).toBe(true);
      });
    });
  });

  describe('Button visibility - DSA panel', () => {
    beforeEach(() => {
      mockOptions.panelType = MappingPanelType.DSA;
    });

    describe('isUnmapSourceFieldButtonVisible', () => {
      it('should be visible when selectedRows has inRiverFieldTypeId for DSA', () => {
        mockOptions.selectedRows.value = [{ inRiverFieldTypeId: '123' }];
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(true);
      });

      it('should be hidden when no inRiverFieldTypeId for DSA', () => {
        mockOptions.selectedRows.value = [{ inRiverEntityTypeId: '123' }]; // Different field
        const { visibility } = useFieldMappingButtons(mockOptions);
        expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(false);
      });
    });

    describe('DSA panel specific restrictions', () => {
      it('should hide DSA-restricted buttons', () => {
        const { visibility } = useFieldMappingButtons(mockOptions);

        expect(visibility.isAddListItemButtonVisible.value).toBe(false);
        expect(visibility.isRemoveListItemButtonVisible.value).toBe(false);
        expect(visibility.isAutomapButtonVisible.value).toBe(false);
        expect(visibility.isExportMappingButtonVisible.value).toBe(false);
        expect(visibility.isImportMappingButtonVisible.value).toBe(false);
        expect(visibility.isSetDefaultLanguageButtonVisible.value).toBe(false);
      });
    });
  });

  describe('Button handlers', () => {
    let consoleErrorSpy: ReturnType<typeof vi.spyOn>;

    beforeEach(() => {
      consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => undefined);
    });

    afterEach(() => {
      consoleErrorSpy.mockRestore();
    });

    describe('save handler', () => {
      it('should call saveHandler successfully', async () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        await handlers.save();
        expect(mockHandlers.saveHandler).toHaveBeenCalledOnce();
      });

      it('should handle save errors gracefully', async () => {
        const error = new Error('Save failed');
        mockHandlers.saveHandler.mockRejectedValue(error);

        const { handlers } = useFieldMappingButtons(mockOptions);
        await handlers.save();

        expect(consoleErrorSpy).toHaveBeenCalledWith('Error saving:', error);
      });
    });

    describe('import handler', () => {
      it('should call importMappingHandler when provided', async () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.importMappingIntoEditor) {
          await handlers.importMappingIntoEditor();
        }
        expect(mockHandlers.importMappingHandler).toHaveBeenCalledOnce();
      });

      it('should handle import errors gracefully', async () => {
        const error = new Error('Import failed');
        mockHandlers.importMappingHandler.mockRejectedValue(error);

        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.importMappingIntoEditor) {
          await handlers.importMappingIntoEditor();
        }

        expect(consoleErrorSpy).toHaveBeenCalledWith('Error importing mapping:', error);
      });
    });

    describe('export handler', () => {
      it('should call exportMappingHandler when provided', async () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.exportCurrentMapping) {
          await handlers.exportCurrentMapping();
        }
        expect(mockHandlers.exportMappingHandler).toHaveBeenCalledOnce();
      });

      it('should handle export errors gracefully', async () => {
        const error = new Error('Export failed');
        mockHandlers.exportMappingHandler.mockRejectedValue(error);

        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.exportCurrentMapping) {
          await handlers.exportCurrentMapping();
        }

        expect(consoleErrorSpy).toHaveBeenCalledWith('Error exporting mapping:', error);
      });
    });

    describe('synchronous handlers', () => {
      it('should call cancelEdit handler', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        handlers.cancelEdit();
        expect(mockHandlers.cancelEditHandler).toHaveBeenCalledOnce();
      });

      it('should call unmapSourceField handler', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        handlers.unmapSourceField();
        expect(mockHandlers.unmapSourceFieldHandler).toHaveBeenCalledOnce();
      });

      it('should call unmapFunction handler', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        handlers.unmapFunction();
        expect(mockHandlers.unmapFunctionHandler).toHaveBeenCalledOnce();
      });

      it('should call mapAutomatically handler when provided', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.mapAutomatically) {
          handlers.mapAutomatically();
        }
        expect(mockHandlers.mapAutomaticallyHandler).toHaveBeenCalledOnce();
      });

      it('should call openDefaultLanguageDialog handler when provided', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.openDefaultLanguageDialog) {
          handlers.openDefaultLanguageDialog();
        }
        expect(mockHandlers.openDefaultLanguageDialogHandler).toHaveBeenCalledOnce();
      });

      it('should call openFunctionSettings handler when provided', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.openFunctionSettings) {
          handlers.openFunctionSettings();
        }
        expect(mockHandlers.openFunctionSettingsHandler).toHaveBeenCalledOnce();
      });

      it('should call openMapEnumDialog handler when provided', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.openMapEnumDialog) {
          handlers.openMapEnumDialog();
        }
        expect(mockHandlers.openMapEnumDialogHandler).toHaveBeenCalledOnce();
      });

      it('should call addListItem handler when provided', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.addListItem) {
          handlers.addListItem();
        }
        expect(mockHandlers.addListItemHandler).toHaveBeenCalledOnce();
      });

      it('should call removeListItem handler when provided', () => {
        const { handlers } = useFieldMappingButtons(mockOptions);
        if (handlers.removeListItem) {
          handlers.removeListItem();
        }
        expect(mockHandlers.removeListItemHandler).toHaveBeenCalledOnce();
      });
    });

    describe('optional handlers', () => {
      it('should handle missing optional handlers gracefully', () => {
        // Remove optional handlers
        delete mockOptions.mapAutomaticallyHandler;
        delete mockOptions.openDefaultLanguageDialogHandler;
        delete mockOptions.openFunctionSettingsHandler;
        delete mockOptions.openMapEnumDialogHandler;
        delete mockOptions.addListItemHandler;
        delete mockOptions.removeListItemHandler;

        const { handlers } = useFieldMappingButtons(mockOptions);

        // These should not throw errors
        if (handlers.mapAutomatically) {
          handlers.mapAutomatically();
        }
        if (handlers.openDefaultLanguageDialog) {
          handlers.openDefaultLanguageDialog();
        }
        if (handlers.openFunctionSettings) {
          handlers.openFunctionSettings();
        }
        if (handlers.openMapEnumDialog) {
          handlers.openMapEnumDialog();
        }
        if (handlers.addListItem) {
          handlers.addListItem();
        }
        if (handlers.removeListItem) {
          handlers.removeListItem();
        }
      });
    });
  });

  describe('isMapEnumButtonVisible', () => {
    it('should return true when isMapEnumButtonVisibleFn returns true', () => {
      mockOptions.isMapEnumButtonVisibleFn = computed(() => true);
      const { visibility } = useFieldMappingButtons(mockOptions);
      expect(visibility.isMapEnumButtonVisible.value).toBe(true);
    });

    it('should return false when isMapEnumButtonVisibleFn returns false', () => {
      mockOptions.isMapEnumButtonVisibleFn = computed(() => false);
      const { visibility } = useFieldMappingButtons(mockOptions);
      expect(visibility.isMapEnumButtonVisible.value).toBe(false);
    });

    it('should return false when isMapEnumButtonVisibleFn is not provided', () => {
      delete mockOptions.isMapEnumButtonVisibleFn;
      const { visibility } = useFieldMappingButtons(mockOptions);
      expect(visibility.isMapEnumButtonVisible.value).toBe(false);
    });
  });

  describe('Edge cases and integration', () => {
    it('should handle empty selectedRows gracefully', () => {
      mockOptions.selectedRows.value = [];
      const { visibility } = useFieldMappingButtons(mockOptions);

      expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(false);
      expect(visibility.isUnmapFunctionButtonVisible.value).toBe(false);
      expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(false);
    });

    it('should handle undefined selectedResourceRows gracefully', () => {
      delete mockOptions.selectedResourceRows;
      mockOptions.selectedRows.value = [];

      const { visibility } = useFieldMappingButtons(mockOptions);
      expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(false);
      expect(visibility.isUnmapFunctionButtonVisible.value).toBe(false);
    });

    it('should handle mixed row states correctly', () => {
      mockOptions.selectedRows.value = [
        { inRiverFieldTypeId: '123', ConverterArgs: '{}' },
        { inRiverFieldTypeId: null },
      ];

      const { visibility } = useFieldMappingButtons(mockOptions);
      expect(visibility.isUnmapSourceFieldButtonVisible.value).toBe(true);
      expect(visibility.isUnmapFunctionButtonVisible.value).toBe(true);
    });

    it('should return consistent handler and visibility objects', () => {
      const result1 = useFieldMappingButtons(mockOptions);
      const result2 = useFieldMappingButtons(mockOptions);

      // Should have the same structure
      expect(Object.keys(result1.handlers)).toEqual(Object.keys(result2.handlers));
      expect(Object.keys(result1.visibility)).toEqual(Object.keys(result2.visibility));
    });
  });

  describe('Complex scenarios', () => {
    it('should handle dynamic mapping with object types correctly', () => {
      mockOptions.selectedRows.value = [{ FormatField: 'testField' }];
      mockOptions.isDynamicMapping = computed(() => true);
      mockOptions.selectedRowIsObjectType = computed(() => true);
      mockOptions.displayedRows = computed(() => [
        { listItemOf: 'testField' },
        { listItemOf: 'testField' },
        { listItemOf: 'otherField' },
      ]);

      const { visibility } = useFieldMappingButtons(mockOptions);
      expect(visibility.isAddListItemButtonVisible.value).toBe(true);
      expect(visibility.isRemoveListItemButtonVisible.value).toBe(true);
    });

    it('should handle function settings for complex ConverterArgs', () => {
      const complexConverterArgs = JSON.stringify({
        transformations: [
          {
            function: { name: 'ComplexFunction' },
            parameters: { setting1: 'value1', setting2: true },
          },
        ],
        metadata: { version: '1.0' },
      });

      mockOptions.selectedRows.value = [{ ConverterArgs: complexConverterArgs }];
      vi.mocked(checkIfIsCustomFunction).mockReturnValue(true);

      const { visibility } = useFieldMappingButtons(mockOptions);
      expect(visibility.isEditFunctionSettingsButtonVisible.value).toBe(true);
    });

    it('should properly differentiate between panel types', () => {
      // Test Standard panel
      mockOptions.panelType = MappingPanelType.Standard;
      const standardResult = useFieldMappingButtons(mockOptions);

      // Test DSA panel
      mockOptions.panelType = MappingPanelType.DSA;
      const dsaResult = useFieldMappingButtons(mockOptions);

      // Standard panel should show more buttons
      expect(standardResult.visibility.isAutomapButtonVisible.value).toBe(true);
      expect(dsaResult.visibility.isAutomapButtonVisible.value).toBe(false);
    });
  });
});
