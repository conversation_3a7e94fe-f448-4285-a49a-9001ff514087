import { describe, it, expect, afterEach, beforeEach, Mock, vi } from 'vitest';
import { isSyndicatePlusPartnersEnabled, isSyndicatePlusWithCoreEnabled } from './moduleEnabledService';
import isFeatureEnabled from '@utils/isFeatureEnabled';
import { AccessService } from '@services';

vi.mock('@utils/isFeatureEnabled');

describe('isSyndicatePlusPartnersEnabled', () => {
  let originalAppData = undefined;
  (isFeatureEnabled as Mock).mockReturnValue(false);

  let originalAccessServiceHasSyndicatePlusV2Access = undefined;

  beforeEach(() => {
    originalAppData = window.appData;
    window.appData = Object.assign({}, originalAppData, { isSyndicatePlusV2Enabled: true });
    originalAccessServiceHasSyndicatePlusV2Access = AccessService.hasSyndicatePlusV2Access;
    AccessService.hasSyndicatePlusV2Access = vi.fn().mockReturnValue(false);
  });

  afterEach(() => {
    window.appData = originalAppData;
    AccessService.hasSyndicatePlusV2Access = originalAccessServiceHasSyndicatePlusV2Access;
  });

  it('returns false if isSyndicatePlusPartnersDisabled is true', () => {
    window.appData = Object.assign({}, window.appData, { isSyndicatePlusPartnersDisabled: true });
    expect(isSyndicatePlusPartnersEnabled()).toBe(false);
  });

  it("returns false if isSyndicatePlusPartnersDisabled is 'true'", () => {
    window.appData = Object.assign({}, window.appData, { isSyndicatePlusPartnersDisabled: 'true' });
    expect(isSyndicatePlusPartnersEnabled()).toBe(false);
  });

  it('returns false if isSyndicatePlusPartnersDisabled is undefined', () => {
    window.appData = {};
    expect(isSyndicatePlusPartnersEnabled()).toBe(true);
  });

  it('returns false if isSyndicateAdvanceEnabled is true and no syndicate plus permissions are enabled', () => {
    window.appData = Object.assign({}, window.appData, { isSyndicateAdvanceEnabled: 'true' });
    (isFeatureEnabled as Mock).mockReturnValue(false);
    expect(isSyndicatePlusPartnersEnabled()).toBe(false);
  });

  it('returns true if isSyndicateAdvanceEnabled is true and a syndicate plus permissions are enabled', () => {
    AccessService.hasSyndicatePlusV2Access = vi.fn().mockReturnValue(true);
    window.appData = Object.assign({}, window.appData, { isSyndicateAdvanceEnabled: 'true' });
    (isFeatureEnabled as Mock).mockReturnValue(false);
    expect(isSyndicatePlusPartnersEnabled()).toBe(true);
  });
});

describe('isSyndicatePlusWithCoreEnabled', () => {
  let originalAppData = undefined;
  (isFeatureEnabled as Mock).mockReturnValue(false);

  beforeEach(() => {
    originalAppData = window.appData;
  });

  afterEach(() => {
    window.appData = originalAppData;
  });

  it('returns false if isSyndicatePlusWithCoreEnabled is false', () => {
    window.appData = { isSyndicatePlusWithCoreEnabled: false };
    expect(isSyndicatePlusWithCoreEnabled()).toBe(false);
  });

  it("returns false if isSyndicatePlusWithCoreEnabled is 'false'", () => {
    window.appData = { isSyndicatePlusWithCoreEnabled: 'false' };
    expect(isSyndicatePlusWithCoreEnabled()).toBe(false);
  });

  it("returns true if isSyndicateAdvanceEnabled is 'true'", () => {
    window.appData = { isSyndicateAdvanceEnabled: 'true' };
    expect(isSyndicatePlusWithCoreEnabled()).toBe(true);
  });

  it('returns false if isSyndicatePlusWithCoreEnabled is undefined', () => {
    window.appData = {};
    expect(isSyndicatePlusWithCoreEnabled()).toBe(false);
  });
});
