<template>
  <div v-if="show" class="panel-overlay">
    <div class="panel" :style="{ maxWidth, minWidth }">
      <div v-if="title" class="panel-header">
        <div class="absolute right-0 top-0">
          <slot name="corner-buttons" />
          <c-tile-btn
            icon="mdi-close"
            data-id="dialog-close-button"
            :tooltip="$t('inri.common.close')"
            small
            class="absolute right-0 top-0"
            @click="emit('close')"
          />
        </div>
        <h2 class="panel-title">{{ title }}</h2>
      </div>
      <div class="panel-content">
        <slot />
      </div>
      <div v-if="$slots.footer" class="panel-footer">
        <slot name="footer" />
      </div>
      <div class="dialog-card-action-buttons px-5 pt-2 pb-10">
        <c-btn data-id="dialog-cancel-button" :label="$t('inri.common.close')" @click="emit('close')" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  maxWidth: {
    type: String,
    default: '450px',
  },
  minWidth: {
    type: String,
    default: '450px',
  },
  showCloseButton: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['close']);
</script>

<style lang="scss" scoped>
.panel-overlay {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
  display: flex;
  justify-content: flex-end;
  align-items: stretch;
}

.panel {
  background-color: white;
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  overflow: hidden;

  @media (max-width: 768px) {
    max-width: 100vw !important;
  }
}

.panel-header {
  padding-bottom: 1rem;
  padding-top: 1.5rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;

  display: flex;
  justify-content: space-between;
  align-items: center;

  background-color: white;
  flex-shrink: 0;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary, #212121);
  text-transform: lowercase;
}

.panel-close-btn {
  margin-left: 8px;

  &:hover {
    background-color: var(--color-grey-lightest, #f5f5f5);
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  background-color: white;
}

.panel-footer {
  padding: 16px;
  background-color: white;
  flex-shrink: 0;
}
</style>
