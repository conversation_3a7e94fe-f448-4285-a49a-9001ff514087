<template>
  <c-layout-with-back-button :title="title">
    <template #right-sidebar>
      <c-tile-btn
        :tooltip-left="$t('core.manage_trading_partners.add_mapping.tooltip')"
        icon="mdi-plus-circle-outline"
        :icon-size="20"
        @click="goToMappingSettingsPage"
      />
      <c-tile-btn
        v-if="isEditButtonVisible"
        icon="mdi-pencil-outline"
        :icon-size="20"
        :tooltip-left="$t('syndicate_plus.common.edit')"
        @click="handleNavigateToEditClick"
      />
      <c-tile-btn
        v-if="isDuplicateButtonVisible"
        icon="mdi-content-copy"
        :icon-size="20"
        :tooltip-left="$t('core.manage_trading_partners.duplicate_mapping')"
        @click="handleMappingDuplicate"
      />
      <c-tile-btn
        v-if="isDeleteButtonVisible"
        :tooltip-left="$t('core.manage_trading_partners.delete')"
        icon="mdi-delete-outline"
        :icon-size="20"
        @click="handleMappingDelete"
      />
    </template>
    <section class="c-inri-section">
      <q-table
        v-model:selected="selectedRows"
        class="format-mappings-table sticky-table-header"
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        dense
        hide-bottom
        separator="cell"
        :pagination="{
          page: 1,
          rowsPerPage: 0,
        }"
        :rows="displayedRows"
        :row-key="getRowKey"
        :columns="displayedColumns"
        :loading="isLoading"
        @row-click="onRowClick"
        @row-dblclick="(_, row) => onRowDoubleClick(_, row, handleNavigateToEditClick)"
      >
        <template #body-cell-source>
          <q-td>{{ $t('core.trading_partners.collections.workarea') }}</q-td>
        </template>
        <template #loading>
          <q-inner-loading showing color="primary" class="inner-loading">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </template>
      </q-table>
      <c-confirm-dialog
        v-if="showConfirmDialog"
        v-model:show="showConfirmDialog"
        :title="$t('core.manage_trading_partners.delete_not_permitted.title')"
        :text="$t('core.manage_trading_partners.delete_not_permitted.text')"
        :text2="$t('core.manage_trading_partners.delete_not_permitted.text2')"
        :list-text="syndicationsWithSelectedMapping"
        :cancel-button-text="$t('core.manage_trading_partners.ok')"
        :hide-confirm-button="true"
        @handle-cancel="showConfirmDialog = false"
      />
      <c-duplicate-mapping-dialog
        v-if="showDuplicateDialog"
        :original-mapping-name="originalMappingName"
        @duplicate-mapping="onDuplicateMapping"
      />
    </section>
  </c-layout-with-back-button>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { DynamicMappingResponse, Mapping } from '@core/interfaces';
import { formatMappingsColumns, dynamicFormatMappingsColumns } from '@core/const';
import { useRouter } from '@composables/useRouter';
import { useCoreTradingPartnersStore } from '@core/stores';
import { useSingleRowSelect } from '@composables/Common';
import {
  getMappingsByFormatFileId,
  deleteMapping,
  checkIfMappingIsUsed,
  fetchDynamicMappingsByFormatId,
  deleteDynamicMapping,
} from '@core/services/Mappings';
import { extractTradingPartnerName } from '@core/services/utils';
import { CConfirmDialog } from '@components';
import { deleteDsaMapping } from '@core/services/DsaMapping';
import { CDuplicateMappingDialog } from '@core/components/TradingPartners';
import useDuplicateMapping from '@core/composables/Mappings/useDuplicateMapping';

const coreTradingPartnersStore = useCoreTradingPartnersStore();

const { coreFormats, dynamicFormats } = storeToRefs(coreTradingPartnersStore);
const { route, goToPage } = useRouter();
const { t } = useI18n();
const { selectedRows, onRowClick, onRowDoubleClick } = useSingleRowSelect<Mapping | DynamicMappingResponse>();
const { duplicateMapping } = useDuplicateMapping();

// Variables
const formatFileId = parseInt(route.params.formatId as string);

// Refs
const isLoading = ref(false);
const mappings = ref<Mapping[]>([]);
const dynamicMappings = ref<DynamicMappingResponse[]>([]);
const showConfirmDialog = ref(false);
const syndicationsWithSelectedMapping = ref<string[]>([]);
const showDuplicateDialog = ref(false);
const originalMappingName = ref('');

// Computed
const title = computed(() => {
  if (isDynamicFormat.value) {
    const formatFile = dynamicFormats.value?.find((formatFile) => formatFile.Id === formatFileId);
    if (!!formatFile) {
      return `mappings for ${formatFile?.Name}`;
    }
  }
  const formatFileName = coreFormats.value?.find((formatFile) => formatFile.Id === formatFileId)?.Name;
  return `mappings for ${formatFileName}`;
});

const selectedMapping = computed(() => selectedRows.value[0]);
const isDynamicFormat = computed(() => route.path.includes('dynamicformats'));
const isDeleteButtonVisible = computed(() => selectedRows.value.length);
const displayedRows = computed(() => (isDynamicFormat.value ? dynamicMappings.value : mappings.value));
const displayedColumns = computed(() => (isDynamicFormat.value ? dynamicFormatMappingsColumns : formatMappingsColumns));
const getRowKey = computed(
  () => (row: Mapping | DynamicMappingResponse) =>
    isDynamicFormat.value ? (row as DynamicMappingResponse).id : (row as Mapping).MappingId
);
const isEditButtonVisible = computed(() => selectedRows.value.length);
const isDuplicateButtonVisible = computed(() => selectedRows.value.length);

// Functions
const fetchMappings = async (): Promise<void> => {
  isLoading.value = true;
  try {
    mappings.value = await getMappingsByFormatFileId(formatFileId);
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const fetchDynamicMappings = async (): Promise<void> => {
  isLoading.value = true;
  try {
    dynamicMappings.value = await fetchDynamicMappingsByFormatId(formatFileId);
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const handleMappingDelete = async (): Promise<void> => {
  if (isDynamicFormat.value) {
    await deleteSelectedDynamicMapping();
    return;
  }

  syndicationsWithSelectedMapping.value = await checkIfMappingIsUsed((selectedMapping.value as Mapping).MappingId);
  if (syndicationsWithSelectedMapping.value.length) {
    showConfirmDialog.value = true;
    return;
  }
  await deleteSelectedMapping();
};

const deleteSelectedMapping = async (): Promise<void> => {
  await deleteRelatedDsaMapping();
  const mappingIsDeleted = await deleteMapping((selectedMapping.value as Mapping).MappingId);
  if (mappingIsDeleted) {
    notify.success(
      t('core.manage_trading_partners.delete_mapping_success', {
        mappingName: (selectedMapping.value as Mapping)?.MappingName,
      })
    );
    selectedRows.value = [];
  } else {
    notify.error(t('core.manage_trading_partners.delete_mapping_error'));
  }

  await fetchMappings();
};

const deleteSelectedDynamicMapping = async (): Promise<void> => {
  await deleteRelatedDsaMapping();
  const mappingIsDeleted = await deleteDynamicMapping((selectedMapping.value as DynamicMappingResponse).id);
  mappingIsDeleted
    ? notify.success(
        t('core.manage_trading_partners.delete_mapping_success', {
          mappingName: (selectedMapping.value as DynamicMappingResponse)?.name,
        })
      )
    : notify.error(t('core.manage_trading_partners.delete_mapping_error'));
  if (mappingIsDeleted) {
    selectedRows.value = [];
  }

  await fetchDynamicMappings();
};

const deleteRelatedDsaMapping = async (): Promise<void> => {
  if (isDynamicFormat.value) {
    const dsaMappingId = JSON.parse((selectedMapping.value as DynamicMappingResponse).data).DsaMappingId;
    if (dsaMappingId) {
      await deleteDsaMapping(dsaMappingId);
    }
    return;
  }

  const mapping = selectedMapping.value as Mapping;
  if (mapping.DsaMappingId) {
    await deleteDsaMapping(mapping.DsaMappingId);
  }
};

const goToMappingSettingsPage = (): void => {
  if (isDynamicFormat.value) {
    goToPage('dynamic-mapping-settings-page');
    return;
  }

  const formatFileName = coreFormats.value?.find((formatFile) => formatFile.Id === formatFileId)?.Name;
  const tradingPartner = extractTradingPartnerName(formatFileName as string);
  goToPage('mapping-settings-page', {
    tradingPartnerId: tradingPartner,
    formatId: formatFileId,
  });
};

const handleNavigateToEditClick = (): void => {
  if (!isDynamicFormat.value) {
    const mapping = selectedMapping.value as Mapping;
    const formatFileName = coreFormats.value?.find((formatFile) => formatFile.Id === formatFileId)?.Name;
    const tradingPartner = extractTradingPartnerName(formatFileName as string);
    goToPage('edit-field-mapping-format-page', {
      mappingId: mapping.MappingId,
      tradingPartnerId: tradingPartner,
      formatId: formatFileId,
    });
    return;
  }

  const dynamicMapping = selectedMapping.value as DynamicMappingResponse;
  goToPage('edit-dynamic-mapping-page', {
    mappingId: dynamicMapping.id,
    tradingPartnerId: dynamicMapping.tradingPartnerId,
    formatId: formatFileId,
  });
};

const handleMappingDuplicate = async (): Promise<void> => {
  if (!selectedMapping.value) {
    return;
  }

  // Set the original mapping name for the dialog
  if (isDynamicFormat.value) {
    originalMappingName.value = (selectedMapping.value as DynamicMappingResponse).name;
  } else {
    originalMappingName.value = (selectedMapping.value as Mapping).MappingName;
  }

  // Show the duplicate dialog
  showDuplicateDialog.value = true;
};

const onDuplicateMapping = async (newMappingName: string): Promise<void> => {
  if (!selectedMapping.value) {
    return;
  }

  const success = await duplicateMapping(selectedMapping.value, newMappingName, isDynamicFormat.value);

  if (success) {
    // Refresh the mappings list
    if (isDynamicFormat.value) {
      await fetchDynamicMappings();
    } else {
      await fetchMappings();
    }

    // Clear selection
    selectedRows.value = [];
  }

  showDuplicateDialog.value = false;
};

// Lifecycle methods
onBeforeMount(async () => {
  isLoading.value = true;
  try {
    await coreTradingPartnersStore.fetchFormats();
    if (isDynamicFormat.value) {
      await fetchDynamicMappings();
    } else {
      await fetchMappings();
    }
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.format-mappings-table {
  max-height: calc(100vh - 150px);
  margin-bottom: 200px;
  min-height: 120px;
}
</style>
