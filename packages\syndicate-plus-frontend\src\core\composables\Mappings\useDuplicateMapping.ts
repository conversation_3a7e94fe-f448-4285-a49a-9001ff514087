import { ref } from 'vue';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { Mapping, DynamicMappingResponse, DynamicMappingDetailsResponse, NewMapping } from '@core/interfaces';
import { getMapping, saveMapping } from '@core/services/Mappings/mappingsApi';
import { fetchDynamicMappingById, saveDynamicMapping } from '@core/services/Mappings/dynamicMappingsApi';

export default function useDuplicateMapping() {
  // Composables
  const { t } = useI18n();

  // Refs
  const isLoading = ref(false);

  // Functions
  const duplicateFileMapping = async (originalMapping: Mapping, newMappingName: string): Promise<boolean> => {
    try {
      isLoading.value = true;
      const originalDetails = await getMapping(originalMapping.MappingId);
      if (!originalDetails) {
        throw new Error('Failed to fetch original mapping details');
      }

      const newMapping: NewMapping = {
        MappingId: null,
        MappingName: newMappingName,
        WorkareaEntityTypeId: originalDetails.WorkareaEntityTypeId,
        OutputEntityTypeId: originalDetails.OutputEntityTypeId,
        EnableSKU: originalDetails.EnableSKU,
        FirstRelatedEntityTypeId: originalDetails.FirstRelatedEntityTypeId,
        SecondRelatedEntityTypeId: originalDetails.SecondRelatedEntityTypeId,
        FormatId: originalDetails.FormatId,
        ImageUrl: null,
        FirstLinkEntityTypeId: originalDetails.FirstLinkEntityTypeId,
        SecondLinkEntityTypeId: originalDetails.SecondLinkEntityTypeId,
        DefaultLanguage: null,
        MappingModelList: originalDetails.MappingModelList || [],
        DsaMappingId: null, // Don't duplicate DSA mapping
      };

      const savedMappingId = await saveMapping(newMapping);
      if (savedMappingId) {
        notify.success(t('core.manage_trading_partners.duplicate_mapping_dialog.save_success'));
        return true;
      } else {
        throw new Error('Failed to save duplicated mapping');
      }
    } catch (error) {
      console.error('Error duplicating regular mapping:', error);
      notify.error(t('core.manage_trading_partners.duplicate_mapping_dialog.save_error'));
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const duplicateDynamicMapping = async (
    originalMapping: DynamicMappingResponse,
    newMappingName: string
  ): Promise<boolean> => {
    try {
      isLoading.value = true;
      const originalDynamicMapping = await fetchDynamicMappingById(originalMapping.id);
      if (!originalDynamicMapping || !originalDynamicMapping.data) {
        throw new Error('Failed to fetch original dynamic mapping details');
      }

      const originalDetails = JSON.parse(originalDynamicMapping.data) as DynamicMappingDetailsResponse;
      const newMappingData = {
        MappingId: null,
        MappingName: newMappingName,
        WorkareaEntityTypeId: originalDetails.WorkareaEntityTypeId,
        OutputEntityTypeId: originalDetails.OutputEntityTypeId,
        FirstRelatedEntityTypeId: originalDetails.FirstRelatedEntityTypeId,
        FirstLinkEntityTypeId: originalDetails.FirstLinkEntityTypeId,
        SecondRelatedEntityTypeId: originalDetails.SecondRelatedEntityTypeId,
        SecondLinkEntityTypeId: originalDetails.SecondLinkEntityTypeId,
        DefaultLanguage: null,
        MappingModelList: originalDetails.MappingModelList || [],
        EnableSKU: originalDetails.EnableSKU,
        FormatId: originalDetails.FormatId,
        ImageUrl: originalDetails.ImageUrl,
        DsaMappingId: null, // Don't duplicate DSA mapping
      } as NewMapping;
      const formatFileId = parseInt(originalDynamicMapping.environmentFormatId || '0');
      const savedMapping = await saveDynamicMapping(formatFileId, newMappingData);
      if (savedMapping) {
        notify.success(t('core.manage_trading_partners.duplicate_mapping_dialog.save_success'));
        return true;
      } else {
        throw new Error('Failed to save duplicated dynamic mapping');
      }
    } catch (error) {
      console.error('Error duplicating dynamic mapping:', error);
      notify.error(t('core.manage_trading_partners.duplicate_mapping_dialog.save_error'));
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const duplicateMapping = async (
    mapping: Mapping | DynamicMappingResponse,
    newMappingName: string,
    isDynamic: boolean
  ): Promise<boolean> => {
    if (isDynamic) {
      return await duplicateDynamicMapping(mapping as DynamicMappingResponse, newMappingName);
    } else {
      return await duplicateFileMapping(mapping as Mapping, newMappingName);
    }
  };

  return {
    isLoading,
    duplicateMapping,
  };
}
