/* Microfrontend CSS Isolation - HIGH SPECIFICITY for HInclude Load Order */

/* Use maximum specificity to ensure our styles always win regardless of load order */
.syndicate-plus-isolated.syndicate-plus-isolated.syndicate-plus-isolated {
  /* Container isolation - minimal approach to avoid position:fixed issues */
  isolation: isolate;
  position: relative;
  z-index: 1;

  /* Reset inherited styles that might conflict */
  font-family: Wallop, Roboto, -apple-system, 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: inherit;
  background: transparent;

  /* Prevent parent styles from bleeding in */
  box-sizing: border-box;

  /* Ensure all child elements use border-box */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  /* Minimal Quasar component fixes - only override what conflicts */
  .q-btn.q-btn {
    font-family: inherit !important;
    text-transform: none !important;
  }

  .q-tab.q-tab {
    font-family: inherit !important;
    text-transform: none !important;
  }

  .q-table.q-table {
    font-family: inherit !important;

    th,
    td {
      font-family: inherit !important;
    }
  }

  .q-field.q-field {
    font-family: inherit !important;

    .q-field__control {
      font-family: inherit !important;
    }
  }

  /* Toast notifications - ensure they appear above everything */
  .v-toast {
    z-index: 9999 !important;
    position: fixed !important;

    &__item {
      font-family: inherit;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 9999;
    }
  }

  /* Ensure utility classes work correctly */
  .flex {
    display: flex !important;
  }
  .items-center {
    align-items: center !important;
  }
  .justify-center {
    justify-content: center !important;
  }
  .text-center {
    text-align: center !important;
  }
  .text-right {
    text-align: right !important;
  }
  .relative {
    position: relative !important;
  }
  .absolute {
    position: absolute !important;
  }
  .block {
    display: block !important;
  }
  .inline-block {
    display: inline-block !important;
  }

  /* Add more common utility classes */
  .hidden {
    display: none !important;
  }
  .visible {
    visibility: visible !important;
  }
  .invisible {
    visibility: hidden !important;
  }
  .w-full {
    width: 100% !important;
  }
  .h-full {
    height: 100% !important;
  }
  .cursor-pointer {
    cursor: pointer !important;
  }
  .cursor-not-allowed {
    cursor: not-allowed !important;
  }
}

/* HInclude-specific overrides */
@media screen {
  .syndicate-plus-isolated {
    /* Ensure the app doesn't break portal layout */
    max-width: 100%;
    overflow: visible;

    /* Prevent margin/padding conflicts */
    margin: 0;
    padding: 0;
  }
}

/* Global CSS for Quasar teleported components - outside of isolation scope */
.q-dialog__backdrop,
.q-dialog,
.q-menu,
.q-tooltip {
  /* Ensure these always use viewport positioning */
  position: fixed !important;
  contain: none !important;
}

/* Ensure Quasar's teleported components work correctly */
body > .q-dialog,
body > .q-menu,
body > .q-tooltip {
  position: fixed !important;
  z-index: 2000 !important;
}
