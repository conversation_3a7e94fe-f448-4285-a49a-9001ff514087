<template>
  <c-layout-with-back-button :title="$t('core.outputs.create_new_output')">
    <template #right-sidebar>
      <c-tile-btn
        :tooltip-left="$t('core.outputs.save_output')"
        icon="mdi-content-save-outline"
        :icon-size="20"
        @click="onSubmit"
      />
      <c-tile-btn icon="mdi-close" :icon-size="20" :tooltip-left="$t('syndicate_plus.common.cancel')" @click="cancel" />
    </template>
    <section class="c-inri-section">
      <q-form ref="form" greedy autofocus>
        <c-grid :cols="{ md: 1 }">
          <div>
            <q-input
              v-model="newOutput.OutputName"
              class="mb-6"
              v-bind="$inri.input"
              :label="$t('core.outputs.add_output.output_name')"
              :rules="[
                $validate.required(),
                $validate.maxLength(50),
                (val) => val.match(/^[a-zA-Z0-9]+$/) || $t('core.outputs.add_output.output_name_error'),
              ]"
            />
            <c-select
              v-model="newOutput.OutputType"
              v-bind="$inri.input"
              data-testid="output-type"
              :options="OutputTypes"
              :label="$t('core.outputs.add_output.output_type')"
              :rules="[$validate.required()]"
              @update:model-value="onOutputTypeChange"
            />
            <div v-if="isCSVOutput">
              <q-input
                v-model="(newOutput.Settings as OutputCSVSettings).Delimiter"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.delimiter')"
                class="output-format"
              />
              <c-select
                v-model="(newOutput.Settings as OutputCSVSettings).DataLevel"
                v-bind="$inri.input"
                :options="DataLevels"
                :label="$t('core.outputs.add_output.data_level')"
                :rules="[$validate.required()]"
              />
            </div>
            <div v-if="isExcelOutput">
              <q-input
                v-model="(newOutput.Settings as OutputExcelSettings).TemplateFilename"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.template_file_name')"
                :rules="[
                  (val) => val.match(/^$|^.*\.(xlsx|xlsm)$/) || $t('core.outputs.add_output.template_file_name_error'),
                ]"
              />
              <q-input
                v-model="(newOutput.Settings as OutputExcelSettings).ExportFilename"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.export_file_name')"
                :rules="[
                  (val) => val.match(/^$|^.*\.(xlsx|xlsm)$/) || $t('core.outputs.add_output.export_file_name_error'),
                ]"
              />
              <!-- Dynamic worksheet pairs -->
              <div v-for="(pair, index) in worksheetPairs" :key="index" class="worksheet-pair">
                <div class="row q-gutter-md items-end">
                  <div class="col">
                    <q-input
                      v-model="pair.worksheetName"
                      v-bind="$inri.input"
                      :label="$t('core.outputs.add_output.worksheet_name')"
                      :rules="[$validate.required()]"
                    />
                  </div>
                  <div class="col">
                    <q-input
                      v-model="pair.startCell"
                      v-bind="$inri.input"
                      :label="$t('core.outputs.add_output.worksheet_start_cell')"
                      :rules="[
                        $validate.required(),
                        (val) => val.match(/^[A-Z]+[0-9]+$/) || $t('core.outputs.add_output.start_cell_error'),
                      ]"
                    />
                  </div>
                  <div class="col-auto">
                    <q-btn
                      flat
                      round
                      color="negative"
                      icon="mdi-delete"
                      size="sm"
                      :style="{ visibility: worksheetPairs.length === 1 ? 'hidden' : 'visible' }"
                      @click="removeWorksheetPair(index)"
                    />
                  </div>
                </div>
              </div>

              <!-- Add worksheet button -->
              <div class="q-mt-md">
                <q-btn
                  flat
                  color="primary"
                  icon="mdi-plus"
                  :label="$t('core.outputs.add_output.add_worksheet')"
                  :disable="!areAllPairsValid"
                  @click="addWorksheetPair"
                />
              </div>
              <q-checkbox
                v-model="(newOutput.Settings as OutputExcelSettings).UseZip"
                v-bind="$inri.checkbox"
                :label="$t('core.outputs.add_output.export_to_zip_folder')"
                class="pb-15px"
                :disable="newOutput.Settings.EnableCompression"
              />
              <q-checkbox
                v-model="(newOutput.Settings as OutputExcelSettings).UseUTF8"
                v-bind="$inri.checkbox"
                :label="$t('core.outputs.add_output.set_encoding_to_utf_8')"
                class="pb-15px"
              />
            </div>
            <div>
              <q-checkbox
                v-model="newOutput.Settings.EnableCompression"
                v-bind="$inri.checkbox"
                :label="$t('core.outputs.add_output.enable_compression')"
                class="pb-15px"
              />
              <q-input
                v-model="newOutput.Settings.OutputFormat"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.output_format')"
                :disable="isOutputFormatDisabled"
                :rules="[$validate.required()]"
                class="output-format"
              />
              <c-select
                v-model="(newOutput.Settings as OutputCSVSettings).DeliveryMethodsList"
                v-bind="$inri.input"
                data-testid="delivery-method"
                :options="Object.values(DeliveryMethod)"
                :label="$t('core.outputs.add_output.delivery_method')"
                multiple
              />
            </div>
            <div v-if="isAzureDelivery" class="section">
              <h3>
                {{ $t('core.outputs.add_output.azure_delivery.settings') }}
              </h3>
              <q-input
                v-model="(newOutput.Settings as OutputSettings).Azure_FileName"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.file_name')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).Azure_ConnectionString"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.connection_string')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).Azure_Container"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.azure_delivery.container')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).Azure_Path"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.path')"
              />
            </div>
            <div v-if="isFTPDelivery" class="section">
              <h3>
                {{ $t('core.outputs.add_output.ftp_delivery.settings') }}
              </h3>
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTP_FileName"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.file_name')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTP_Host"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.host')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTP_UserName"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.username')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTP_Password"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.password')"
                type="password"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTP_Path"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.path')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTP_Port"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.port')"
              />
            </div>
            <div v-if="isFTPSDelivery" class="section">
              <h3>
                {{ $t('core.outputs.add_output.ftps_delivery.settings') }}
              </h3>
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTPS_FileName"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.file_name')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTPS_Host"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.host')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTPS_UserName"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.username')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTPS_Password"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.password')"
                type="password"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTPS_Path"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.path')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).FTPS_Port"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.port')"
              />
            </div>
            <div v-if="isHTTPDelivery" class="section">
              <h3>
                {{ $t('core.outputs.add_output.http_delivery.settings') }}
              </h3>
              <q-input
                v-model="(newOutput.Settings as OutputSettings).Http_Filename"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.file_name')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).Http_Url"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.http_delivery.url')"
              />
            </div>
            <div v-if="isSFTPDelivery" class="section">
              <h3>
                {{ $t('core.outputs.add_output.sftp_delivery.settings') }}
              </h3>
              <q-input
                v-model="(newOutput.Settings as OutputSettings).SFTP_FileName"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.file_name')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).SFTP_Host"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.host')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).SFTP_UserName"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.username')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).SFTP_Password"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.password')"
                type="password"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).SFTP_Path"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.path')"
              />
              <q-input
                v-model="(newOutput.Settings as OutputSettings).SFTP_Port"
                v-bind="$inri.input"
                :label="$t('core.outputs.add_output.port')"
              />
            </div>
          </div>
        </c-grid>
      </q-form>
    </section>
  </c-layout-with-back-button>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { useRouter } from '@composables/useRouter';
import { SettingsTabNames, DeliveryMethod } from '@core/enums';
import { DataLevels, OutputTypes } from '@core/const/Outputs';
import { useOutputSettings } from '@core/composables/Output';
import { OutputCSVSettings, OutputExcelSettings, OutputSettings } from '@core/interfaces/Outputs';

const { goToPage } = useRouter();
const {
  newOutput,
  worksheetPairs,
  areAllPairsValid,
  isMultiTabMode,
  firstWorksheetName,
  addWorksheetPair,
  removeWorksheetPair,
  isOutputFormatDisabled,
  isCSVOutput,
  isExcelOutput,
  isAzureDelivery,
  isFTPDelivery,
  isFTPSDelivery,
  isHTTPDelivery,
  isSFTPDelivery,
  saveNewOutput,
  enableCompressionIsChanged,
  onOutputTypeChange,
} = useOutputSettings();

// Variables
const { t } = useI18n();

// Refs
const form = ref();

// Functions
const onSubmit = async () => {
  form.value.validate().then((success) => {
    if (success) {
      handleSave();
    } else {
      notify.error(t('core.outputs.add_output.validation_error'), {
        position: 'bottom-right',
      });
    }
  });
};

const cancel = () => {
  goToPage('manage-trading-partners-page', { tabName: SettingsTabNames.OUTPUTS });
};

const handleSave = async () => {
  const response = await saveNewOutput();
  if (!response) {
    notify.error(t('core.outputs.add_output.error'), {
      position: 'bottom-right',
    });
    return;
  }
  notify.success(t('core.outputs.add_output.success'), {
    position: 'bottom-right',
  });
  goToPage('manage-trading-partners-page', { tabName: SettingsTabNames.OUTPUTS });
};

// Lifecycle methods
watch(
  () => newOutput.value.Settings.EnableCompression,
  () => {
    enableCompressionIsChanged();
  }
);
</script>

<style lang="scss" scoped>
:deep(.c-inri-input.c-inri-input--default:not(.q-textarea) .q-field__control) {
  min-width: 150px;
  max-height: 41px;
}

.c-inri-section {
  overflow-y: auto;
}

.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
    font-weight: normal;
  }

  .action-buttons {
    padding-top: 10px;
  }
}

.worksheet-pair {
  margin-top: 16px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>
