import { Locator, Page } from '@playwright/test';
import { PreflightPage } from '@pages/notCore/preflight.page';
import { BasePage } from '@pages/basePage.page';
import { FilterPanelPage } from '@pages/notCore/filterPanel.page';
import { expect } from '@playwright/test';
import {getPlatformModifierKey} from "@src/helpers/keyboard-modifiers";

export class ProductTabPage extends BasePage {
  private page: Page;
  readonly productCards: Locator;
  readonly productRows: Locator;
  readonly runPreflightButton: Locator;
  readonly exportMenuButton: Locator;
  readonly collectionsTable: Locator;
  readonly searchField: Locator;
  readonly openFilterButton: Locator;
  readonly closeFilterButton: Locator;
  readonly switchViewModeButton: Locator;
  readonly productCard: Locator;
  readonly selectAll: Locator;
  // Details locators
  readonly detailsMenuButton: Locator;
  readonly viewDetailsButton: Locator;
  readonly viewMediaButton: Locator;
  readonly viewPriceButton: Locator;
  readonly maxFiveItemsAllowedNotification: Locator;
  readonly aPlusContentProductCards: Locator;
  readonly aPlusContentButton: Locator;
  // Collection locators
  readonly addToCollectionButton: Locator;
  readonly addCollectionButton: Locator;
  readonly collectionList: Locator;
  readonly collectionListItems = (text: string): Locator =>
    this.page.getByRole('listbox').getByText(text, { exact: true });

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.productCards = page.locator('.c-product-small-card');
    this.productRows = page.locator('.q-virtual-scroll__content tr');
    this.runPreflightButton = page.getByTestId('menu-preflight');
    this.exportMenuButton = page.getByTestId('export_button');
    this.collectionsTable = page.locator('[data-id="collections-table"]');
    this.searchField = page.getByPlaceholder('search');
    this.openFilterButton = page.locator('button[aria-label="filter"]');
    this.closeFilterButton = page.getByLabel('close');
    this.switchViewModeButton = page.locator('.test--toggle-view');
    this.productCard = page.locator('.c-product-card');
    this.selectAll = page.getByLabel('select all');
    this.detailsMenuButton = page.getByTestId('menu-details-button');
    this.viewDetailsButton = page.getByTestId('menu-details');
    this.viewMediaButton = page.getByText('view media');
    this.viewPriceButton = page.getByLabel('view price');
    this.maxFiveItemsAllowedNotification = page
      .locator('.q-notifications__list')
      .locator('.q-notification')
      .locator('.q-notification__wrapper');
    this.aPlusContentProductCards = page.locator('.c-product-card').filter({ has: this.page.getByLabel('a+') });
    this.aPlusContentButton = page.getByTestId('aplus_preflight_button');
    this.addToCollectionButton = page.getByLabel('add to collection');
    this.addCollectionButton = page.getByRole('button', { name: 'add', exact: true });
    this.collectionList = page.locator('[placeholder="collection"]');
  }

  async scrollToBottom() {
    await this.page.getByTestId('products-spinner').waitFor({ state: 'hidden' });
    const visibleProductCards = await this.productCards.all();
    visibleProductCards.length && (await visibleProductCards[visibleProductCards.length - 1].click());
  }

  async getCurrentPage() {
    return await this.page.innerHTML('body');
  }

  async openFilterPanel(): Promise<FilterPanelPage> {
    await this.openFilterButton.click();
    return new FilterPanelPage(this.page);
  }

  async search(text: string) {
    await this.searchField.click();
    await this.searchField.fill(text);
    // The application automatically triggers a search if the search value is an empty string
    text && (await this.searchField.press('Enter'));
  }

  async getProductDescriptions(): Promise<string[]> {
    const results = [];
    const products = await this.productCards.all();
    for (const product of products) {
      const text = await product.locator('.description').textContent();
      results.push(text);
    }
    return results;
  }

  async selectProducts(names: string[]) {
    const modifierKey = getPlatformModifierKey();
    await this.page.keyboard.down(modifierKey);
    for (const name of names) {
      await this.page.getByText(name).click();
    }
    await this.page.keyboard.up(modifierKey);
  }

  async runPreflight(): Promise<PreflightPage> {
    await this.exportMenuButton.click();
    await this.runPreflightButton.click();
    return new PreflightPage(this.page);
  }

  async switchViewMode(): Promise<void> {
    await this.switchViewModeButton.click();
  }

  async getRowCountForTableContainingText(text: string): Promise<number> {
    const specTable = this.page.locator('table', { has: this.page.locator(`text="${text}"`) });
    return specTable.locator('tr').count();
  }

  async selectCollection(collectionName: string): Promise<void> {
    // This is a workaround for the collection list not being visible.
    await expect(async () => {
      await this.collectionList.click();
      await expect(this.collectionListItems(collectionName)).toBeVisible({ timeout: 1000 });
    }, 'could not select collection name').toPass();
    await this.collectionListItems(collectionName).click();
  }

  async selectCollectionInFilters(text: string): Promise<void> {
    await expect(async () => {
      await this.openFilterButton.click();
      await this.page.getByTestId('products-filter-spinner').waitFor({ state: 'hidden' });
      const selectCollection = this.page.getByLabel(text);
      await selectCollection.click();
    }, 'could not select collection name').toPass();
  }
}
